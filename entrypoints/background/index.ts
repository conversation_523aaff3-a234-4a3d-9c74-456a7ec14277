import { myNotice<PERSON>ist, noticeSuccessCallBack } from "@/api/message";
import { logout } from "@/api/user.ts";
import registerFetchMessageHandler from "@/entrypoints/background/handlers/fetchMessageHandler.ts";
import registerFetchSSEMessageHandler from "@/entrypoints/background/handlers/fetchSSEMessageHandler.ts";
import { handlePublicApi, handlePublicApiSSE, sendMessageToPopup, sendMessageToAllPopup } from "@/utils/messagebus.ts";
import mqttInstance from "@/utils/mqttInstance"; // 引入 MQTT 实例
import { swManager } from "@/utils/serviceWorkerManager";
import { getTenantId, getToken } from "@/utils/auth.ts";

/** 初始化插件API行为 */
function initializingCrxBehavior() {
  // 将浏览器插件badge-click的默认行为改为打开侧边栏
  if (browser.action) {
    if (browser["sidePanel"]) {
      browser["sidePanel"].setPanelBehavior({ openPanelOnActionClick: true });
    }
  }
}

/** 注册插件上下文菜单 */
function registerContextMenu() {
  browser.contextMenus.create({
    id: "1",
    title: "便签",
    contexts: ["page", "selection"],
  });
  browser.contextMenus.create({
    id: "4",
    title: "创建普通便签",
    parentId: "1",
    contexts: ["page", "selection"],
  });
  browser.contextMenus.create({
    id: "5",
    title: "关键信息提取",
    parentId: "1",
    contexts: ["page", "selection"],
  });
  browser.contextMenus.create({
    id: "6",
    title: "财务公司ai场景收集",
    parentId: "1",
    contexts: ["page", "selection"],
  });
  browser.contextMenus.onClicked.addListener(async (info, tab) => {
    const menuId: number | string = info.menuItemId;
    let notesType = "";
    if (menuId == "4") notesType = "createNote";
    sendMessageToPopup({ type: notesType });
    const token = await getToken();
    const tenantId = await getTenantId();
    if ((menuId === "5" || menuId === "6") && tab?.id) {
      // 关键信息提取： /keyInfomation-extract  财务公司ai场景收集： /financial-collection
      let url = menuId === "5" ? "keyInfomation-extract" : "financial-collection";
      let iframeUrl = `${import.meta.env["VITE_TOOLBOX_URL"]}/#/${url}?tenantid=${tenantId}&token=${token}&agentId=${tenantId}`;
      chrome.scripting.executeScript({
        target: { tabId: tab.id },
        args: [iframeUrl], // 传入动态 iframe src
        func: (iframeUrl) => {
          const pageText = document.body.innerText || "";

          // 如果已存在先清理
          const oldModal = document.getElementById("my-custom-modal");
          if (oldModal) oldModal.remove();

          // 插入样式
          const style = document.createElement("style");
          style.id = "custom-modal-style";
          style.innerHTML = `
          #my-custom-modal {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            height: 80%;
            background: white;
            z-index: 999999;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.2);
            overflow: hidden;
          }
          #my-custom-modal iframe {
            width: 100%;
            height: 100%;
            border: none;
          }
          #my-modal-close {
            position: absolute;
            top: 8px;
            right: 8px;
            z-index: 1000000;
            font-size: 18px;
            cursor: pointer;
          }
        `;
          document.head.appendChild(style);

          // 构建 modal
          const modal = document.createElement("div");
          modal.id = "my-custom-modal";

          const closeBtn = document.createElement("div");
          closeBtn.id = "my-modal-close";
          closeBtn.innerHTML = `<svg
            viewBox="64 64 896 896"
            focusable="false"
            data-icon="close"
            width="22"
            height="22"
            fill="currentColor"
            aria-hidden="true"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path d="M563.8 512l182.1-182.1a7.92 7.92 0 000-11.3l-48.4-48.4a7.92
              7.92 0 00-11.3 0L504 452.2 321.9 270.1a7.92 7.92 0 00-11.3 0l-48.4
              48.4a7.92 7.92 0 000 11.3L452.2 512 270.1 694.1a7.92 7.92 0 000
              11.3l48.4 48.4a7.92 7.92 0 0011.3 0L504 571.8l182.1
              182.1a7.92 7.92 0 0011.3 0l48.4-48.4a7.92 7.92 0 000-11.3L563.8 512z"/>
          </svg>`;
          closeBtn.onclick = () => {
            modal.remove();
          };
          modal.appendChild(closeBtn);

          const iframe = document.createElement("iframe");
          iframe.src = iframeUrl;
          modal.appendChild(iframe);

          document.body.appendChild(modal);

          // 向 iframe 发送页面内容
          // 等子组件准备好了之后，开始发送
          window.addEventListener("message", (event) => {
            if (event.data?.type === "ready") {
              iframe.contentWindow?.postMessage(
                {
                  type: "pageText",
                  text: pageText,
                  url: window.location.href,
                },
                "*",
              );
            }
          });
        },
      });
    }
  });
}

// eslint-disable-next-line require-jsdoc
function initializeAlarm() {
  chrome.cookies.onChanged.addListener(({ cookie, removed }) => {
    if ("scrm.sino-bridge.com" == cookie.domain && cookie.name == "userInfo") {
      if (removed) {
        // 登出
        logout().then(() => {
          browser.storage.local.clear();
        });
      } else {
        // 登录
        cookie.value &&
          browser.storage.local.set({
            userInfo: cookie.value,
          });
      }
    }
  });
}

let notificationsList = [];
// 获取未发送通知的列表，并回调
browser.alarms.onAlarm.addListener(function (alarm) {
  if (alarm.name === "sendChromeMessage") {
    myNoticeList({ readFlag: "read_flag_false", noticeFlag: "notice_flag_false" })
      .then((res) => {
        if (res.code == 401) {
          browser.alarms.clear("sendChromeMessage");
          return;
        }
        if (res.data && res.data.length > 0) {
          notificationsList = res.data;
          res.data.forEach((item) => {
            const id = `${item.noticeRecId}-${item.busiType}`;
            browser.notifications
              .create(id, {
                type: "basic",
                iconUrl: browser.runtime.getURL("/images/logo.png"), // 通知图标路径
                title: item.msgTitle,
                message: item.busiType === "busi_type_version" ? "点击去下载" : "有人@你，赶紧看看",
              })
              .then((notificationId) => {
                let id = notificationId.split("-")[0];
                noticeSuccessCallBack([id]).then((res) => {
                  console.debug("用户已经收到通知了");
                });
              })
              .catch((error) => {
                console.error("通知发送失败:", error);
              });
            setTimeout(() => {
              browser.notifications.clear(id);
            }, 5000);
          });
        }
      })
      .catch((error) => {
        if (error.code == 401) {
          browser.alarms.clear("sendChromeMessage");
          return;
        }
      });
  }
});
function createTabs() {
  chrome.tabs.onCreated.addListener((tab) => {
    // 排除不需要的标签页
    if (!["设置 - AI办公助手", "扩展程序", "新标签页"].includes(tab.title)) {
      // 监听新创建的标签页的加载状态
      const tabLoadListener = (tabId: number, changeInfo: chrome.tabs.TabChangeInfo, updatedTab: chrome.tabs.Tab) => {
        // 只处理我们关注的那个标签页
        if (tabId === tab.id && changeInfo.status === "complete") {
          // 通知所有页面有新标签页创建
          setTimeout(() => {
            sendMessageToAllPopup({
              type: "tabCreated",
              tab: {
                id: updatedTab.id,
                title: updatedTab.title,
                url: updatedTab.url,
                favIconUrl: updatedTab.favIconUrl || browser.runtime.getURL("/images/ico.png"),
              },
            });
          }, 3000);
          // 移除监听器，避免重复处理
          chrome.tabs.onUpdated.removeListener(tabLoadListener);
        }
      };
      // 添加标签页更新监听器
      chrome.tabs.onUpdated.addListener(tabLoadListener);
    }
  });
}

// 消息通知点击事件
browser.notifications.onClicked.addListener((notificationId) => {
  const type = notificationId.split("-")[1];
  if (type === "busi_type_version") {
    const OFFICIAL_URL = import.meta.env["VITE_OFFICIAL_URL"];
    chrome.tabs.create({ url: `${OFFICIAL_URL}/version` });
    return;
  }
  browser.tabs.create({
    url: browser.runtime.getURL("/options.html?message=3"),
  });
  // browser.runtime.sendMessage({ type: "updateOptions"});

  // window.open(browser.runtime.getURL("/options.html?note=1"), "_blank");
  // chrome.runtime.sendMessage({ action: "getTabs" }, (response) => {
  //   if (response) {
  //     // 显示所有标签页的信息
  //     let num = 0;
  //     response.forEach((tab) => {
  //       if (tab.url.includes("chrome-extension://") && tab.url.includes("options.html")) {
  //         num++;
  //       }
  //     });
  //     window.open(browser.runtime.getURL("/options.html?note=1"), "_blank");
  //   }
  // });
});
// 监听缓存看用户信息是否有更新 并创建定时器
browser.storage.onChanged.addListener((changes) => {
  if (Object.prototype.hasOwnProperty.call(changes, "userInfo")) {
    if (changes.userInfo.newValue) {
      browser.alarms.create("sendChromeMessage", {
        periodInMinutes: 1,
      });
    } else {
      browser.alarms.clear("sendChromeMessage");
    }
  }
});

// 转发popup与content.js之间的消息
// eslint-disable-next-line require-jsdoc
function publicMessageListener() {
  // 使用状态管理器检查是否已注册
  if (swManager.isInitialized("messageListenerRegistered")) {
    console.debug("消息监听器已经注册，跳过重复注册");
    return;
  }

  console.debug("开始注册消息监听器...");

  browser.runtime.onMessage.addListener((message: any, sender: any, sendResponse: any) => {
    if (message.fetchType == "fetch" && message.api && message.type == "send") {
      // 调用公共处理接口的方法
      handlePublicApi(message.api, message.params);
    }
    if (message.fetchType == "fetchSSE" && message.url && message.type == "sendSSE") {
      // background调用发送sse请求方法
      handlePublicApiSSE(message.url, message.headers, message.body, message.query, message.instruct);
    }
    // 截图  接受orc发送的开启截图的消息并转发到content.js
    if (message.type === "screenshot") {
      browser.tabs?.query({ active: true, currentWindow: true }).then(async (tabs) => {
        try {
          await browser.scripting.insertCSS({
            target: {
              tabId: tabs[0].id,
            },
            files: ["snipping/inject.css"],
          });
          await browser.scripting.executeScript({
            target: {
              tabId: tabs[0].id,
            },
            files: ["snipping/inject.js"],
          });
        } catch (e) {
          console.error(e);
        }
      });
    } else if (message.type === "captured") {
      const { left, top, width, height } = message;
      if (!width || !height) {
        return;
      }
      browser.tabs
        .captureVisibleTab(sender.tab.windowId, {
          format: "png",
        })
        .then(async (href) => {
          sendMessageToPopup({ type: "captured", href, left, top, width, height });
        });
    } else if (message.type === "mqttAndGoLogin") {
      const { uuid } = message;
      mqttInstance.subscribe(`/mqtt/topic/sino/lamp/oauth/token/login/${uuid}`, (mqMsg) => {
        console.log("接收官网消息-登录=>", `/mqtt/topic/sino/lamp/oauth/token/login/${uuid}`, mqMsg);
        const msg = JSON.parse(mqMsg);
        console.log("接收官网消息-登录消息=1>", msg);
        sendMessageToPopup({ type: "mqttAndGoLogin", refreshToken: msg?.refreshToken, tenantId: msg?.tenantId });
      });
    } else if (message.type === "mqttAndLogout") {
      const { userId } = message;
      mqttInstance.subscribe(`/mqtt/topic/sino/lamp/oauth/token/logout/${userId}`, (mqMsg) => {
        console.log("接收官网消息-退出登录=>", `/mqtt/topic/sino/lamp/oauth/token/logout/${userId}`, mqMsg);
        sendMessageToPopup({ type: "mqttAndLogout" });
      });
    } else if (message.type === "mqttResourceChange") {
      const { userId, tenantId } = message;
      mqttInstance.subscribe(`/mqtt/topic/sino/lamp/oauth/role/update/${userId}/${tenantId}`, (mqMsg) => {
        console.log("接收后台消息-权限变更=>", `/mqtt/topic/sino/lamp/oauth/role/update/${userId}/${tenantId}`, mqMsg);
        sendMessageToPopup({ type: "mqttResourceChange" });
      });
    } else if (message.type === "mqttPointChange") {
      const { userId, tenantId } = message;
      mqttInstance.subscribe(`/mqtt/topic/sino/lamp/point/update/${userId}/${tenantId}`, (mqMsg) => {
        console.log("接收后台消息-积分变化=>", `/mqtt/topic/sino/lamp/point/update/${userId}/${tenantId}`, mqMsg);
        const msg = JSON.parse(mqMsg);
        sendMessageToAllPopup({ type: "mqttPointChange", point: msg?.point, totalPoint: msg?.totalPoint });
      });
    } else if (message.type === "endMqtt") {
      mqttInstance.destroy();
    } else if (message.type === "openChat") {
      sendMessageToPopup(message);
    }
    // 截图  接受content,js发送的截图数据并转发到popup
    if (message.imgUrl) {
      sendMessageToPopup(message);
    }

    /** 注册图片提取监听器 */
    if (message.extractImg64) {
      chrome.storage.local.set({ extractImg64: message.extractImg64 });
      chrome.storage.local.set({ isImgType: "1" });
      chrome.windows.getCurrent((res) => {
        chrome.sidePanel.open({
          windowId: res.id || -1,
        });
      });
    }

    if (message.action === "getTabs") {
      // 查询所有标签页
      browser.tabs &&
        browser.tabs.query({}).then((tabs) => {
          const data = tabs.filter((item) => !["设置 - AI办公助手", "扩展程序", "新标签页"].includes(item.title));
          sendResponse(data);
        });
    } else if (message.action === "getSelectedTabsContent") {
      const tabIds = message.tabIds;
      const promises = tabIds.map((tabId) => {
        return new Promise((resolve) => {
          chrome.scripting.executeScript(
            {
              target: { tabId: tabId },
              func: () => {
                const linkList = document.querySelectorAll("link");
                const hrefs = Array.from(linkList).map((link) => link.href || "");
                let iconLink = "";
                let i = 0;
                hrefs.forEach((item, index) => {
                  if (item.includes(".")) {
                    let arr = item.split(".");
                    if (arr[arr.length - 1] == "svg" || arr[arr.length - 1] == "ico") {
                      if (i < 1) {
                        i = index + 1;
                      }
                    }
                  }
                });
                if (i == 0) {
                  iconLink = "";
                } else {
                  iconLink = hrefs[i - 1] ? hrefs[i - 1] : "";
                }
                return {
                  title: document.title,
                  innerText: document.body.innerText,
                  icon: iconLink || "",
                };
              },
            },
            ([result]) => {
              resolve({ tabId: tabId, innerText: result.result });
            },
          );
        });
      });
      Promise.all(promises).then((results) => {
        sendResponse(results);
      });
    }

    // 清除Cookie
    if (message.ChromeClearCookie) {
      browser.cookies.remove({
        url: import.meta.env["VITE_AUTHORIZE_DOMAIN"],
        name: "userInfo",
      });
      browser.cookies.remove({
        url: import.meta.env["VITE_AUTHORIZE_DOMAIN"],
        name: import.meta.env["VITE_API_HEADER_KEY"],
      });
      browser.cookies.remove({
        url: import.meta.env["VITE_AUTHORIZE_DOMAIN"],
        name: "tokenObj",
      });
      browser.storage.local.remove("user");
    }
    if (message.type === "wakeUp") {
      sendResponse({ status: "success" }); // 向发送者返回响应
    }
    return true;
  });

  // 标记消息监听器已注册
  swManager.markInitialized("messageListenerRegistered", true);
  console.debug("消息监听器注册完成");
}

export default defineBackground(() => {
  createTabs();
  initializingCrxBehavior();
  // 注册普通web请求消息处理器
  registerFetchMessageHandler();
  // 注册sse请求消息处理器
  registerFetchSSEMessageHandler();
  initializeAlarm();
  publicMessageListener();
  registerContextMenu();
  // 使用状态管理器检查是否已经初始化过
  if (swManager.isInitialized("backgroundInitialized")) {
    console.debug("Background script 已经初始化过，跳过重复初始化");
    return;
  }

  console.debug("Background script 开始初始化...");

  // 使用安全初始化方法
  const success = swManager.safeInit("backgroundInitialized", () => {
    createTabs();
    initializingCrxBehavior();

    // 使用状态管理器安全注册各个组件
    swManager.safeInit("fetchHandlerRegistered", registerFetchMessageHandler);
    swManager.safeInit("sseHandlerRegistered", registerFetchSSEMessageHandler);

    initializeAlarm();
    publicMessageListener(); // 这个函数内部已经有状态检查
    registerContextMenu();
  });

  if (success) {
    console.debug("Background script 初始化完成");
  } else {
    console.error("Background script 初始化失败");
  }
});
