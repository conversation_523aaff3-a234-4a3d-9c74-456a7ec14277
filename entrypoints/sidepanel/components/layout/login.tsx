import { LOGIN_MENU_ID } from "@/config/menu";
import { <PERSON><PERSON>, Card, Flex, Form, message, Typography } from "antd";
import React, { useEffect } from "react";
import { wordList } from "@/api/smartMenu";
import { usePermissions } from "../PermissionProvider";
import "./login.less";
import { buildShortUUID } from "@/utils/uuid";

const { Title } = Typography;
const Login: React.FC<{ handleNavigate }> = ({ handleNavigate }) => {
  const { setPermissions, setUserInfo } = usePermissions();

  const fetchRequest = useFetchRequest();

  // 当前用户id
  useEffect(() => {
    browser.runtime.onMessage.addListener(handleSubscribe);
    return () => {
      browser.runtime.onMessage.removeListener(handleSubscribe);
      // 销毁MQTT连接
      // browser.runtime.sendMessage({ type: "endMqtt" });
    };
  }, []);

  const handleSubscribe = async function (message) {
    switch (message.type) {
      case "mqttAndGoLogin": {
        let { refreshToken, tenantId } = message;
        getToken(refreshToken, tenantId);
        break;
      }
      default:
        break;
    }
  };

  const getToken = async (refreshToken, tenantId) => {
    fetchRequest({
      api: "refreshGetToken",
      params: {
        refreshToken: refreshToken,
      },
      callback(res1) {
        browser.storage.local
          .set({
            tenantId: tenantId,
            token: res1.data.token,
            refreshTokenKey: res1.data.refreshToken,
          })
          .then(() => {
            if (res1 || res1.code === 200) {
              // fetchRequest({
              //   api: "switchTenantAndOrg",
              //   params: {
              //     tenantId: tenantId,
              //   },
              //   callback(res2) {
              // if (res2 || res2.code === 0) {
              // browser.storage.local
              //   .set({
              //     token: res2.data.token,
              //     refreshTokenKey: res2.data.refreshToken,
              //   })
              //   .then(() => {
              fetchRequest({
                api: "getCurrentUserInfo",
                params: {},
                callback(res3) {
                  if (res3 || res3.code === 200) {
                    logoutSubscribe(res3.data.id); // 获取到用户id，订阅退出消息
                    resourceSubscribe(res3.data.id, tenantId); // 获取用户id，订阅权限变更消息
                    pointSubscribe(res3.data.id, tenantId); // 获取用户id，订阅积分变更消息
                    // 获取用户有无设置存知识库
                    fetchRequest({
                      api: "settingInfo",
                      params: {},
                      callback: (info) => {
                        if (info.code === 200) {
                          if (info.data) {
                            browser.storage.local.set({
                              settingInfo: info.data,
                            });
                          }
                        }
                      },
                    });
                    // 在 wordList API 返回后再执行 getResource API
                    fetchRequest({
                      api: "getResource",
                      params: {},
                      callback(resource) {
                        const user = {
                          id: res3.data.id,
                          nickName: res3.data.nickName,
                          position: res3.data.position,
                          mobile: res3.data.mobile,
                          gender: res3.data.gender,
                          email: res3.data.email,
                          bizMail: res3.data.email,
                          avatar: res3.data.avatar,
                          deptName: res3.data.deptName,
                          corpName: res3.data.corpName,
                        };
                        setUserInfo(user);
                        cacheSet("userInfo", JSON.stringify(user));
                        const point = {
                          effectivePoint: res3.data.baseEmployee?.effectivePoint,
                          totalPoint: res3.data.baseEmployee?.totalPoint,
                        };
                        cacheSet("point", JSON.stringify(point));
                        if (resource || resource.code === 200) {
                          setPermissions(resource.data.resourceList);
                          cacheSet("permissions", JSON.stringify(resource.data.resourceList));
                          handleNavigate(resource.data.resourceList, user);
                        }
                      },
                    });
                    // 获取用户划词菜单是否是显示
                    // fetchRequest({
                    //   api: "wordList",
                    //   params: {},
                    //   callback(smart) {
                    //     if (smart || smart.code === 200) {
                    //       const [, ...list] = smart.data;
                    //       const menuData = { isShowSmartMenu: smart.data[0].enabled, websiteList: list };
                    //       browser.storage.local.set({
                    //         smartMenu: menuData,
                    //       });
                    //     }
                    //   },
                    // });
                  } else {
                    message.error(!res3 ? "网络异常，请稍后再试" : res3.msg);
                  }
                },
              });
              // });
              // }
              //   },
              // });
            } else {
              message.error(!res1 ? "网络异常，请稍后再试" : res1.msg);
            }
          });
      },
    });
  };

  /** 跳转到【我的】 */
  const handleToLogin = async (operationType: string) => {
    const uuid = buildShortUUID();
    // 使用 MQTT 实例订阅消息
    browser.runtime.sendMessage({
      type: "mqttAndGoLogin",
      uuid: uuid,
    });
    // 跳转官网登录页面
    window.open(
      `${import.meta.env.VITE_OFFICIAL_URL}?uuid=${uuid}&fromType=plugIn&operationType=${operationType}`,
      "_blank",
    );
  };

  // 订阅监听官网退出登录
  const logoutSubscribe = (userId: string) => {
    // 使用 MQTT 实例订阅消息
    browser.runtime.sendMessage({
      type: "mqttAndLogout",
      userId: userId,
    });
  };

  // 订阅监听后台权限变更
  const resourceSubscribe = (userId: string, tenantId: string) => {
    // 使用 MQTT 实例订阅消息
    browser.runtime.sendMessage({
      type: "mqttResourceChange",
      userId: userId,
      tenantId: tenantId,
    });
  };

  // 订阅监听后端积分变化
  const pointSubscribe = (userId: string, tenantId: string) => {
    // 使用 MQTT 实例订阅消息
    browser.runtime.sendMessage({
      type: "mqttPointChange",
      userId: userId,
      tenantId: tenantId,
    });
  };

  return (
    <Flex align="center" justify="center" className="login">
      <Card className="login-content" bordered={false}>
        <Title level={4} style={{ margin: "10px 0px 25px 0px!important" }}>
          智能办公助手
        </Title>
        <div className="login-main-box">
          <div className="login-bg-bubble" />
          <img src={browser.runtime.getURL("/images/login/login-main.png")} className="login-main" />
          <img src={browser.runtime.getURL("/images/login/login-tip.png")} className="login-tip" />
        </div>
        <Form name="login" initialValues={{ remember: true }}>
          <Form.Item style={{ marginBottom: 16 }}>
            <Button type="primary" block onClick={() => handleToLogin("login")}>
              登录
            </Button>
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Button type="default" block onClick={() => handleToLogin("register")}>
              注册
            </Button>
          </Form.Item>
        </Form>
      </Card>
      <Flex className="login-bg-footer" vertical gap={8}>
        <span>© 2024 智能办公助手 版权所有</span>
        <span> ICP 备案号：京ICP备2024010101号-1</span>
      </Flex>
    </Flex>
  );
};

export default Login;
