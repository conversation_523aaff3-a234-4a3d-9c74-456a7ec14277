/** 侧边栏布局组件 */
import React, { useEffect, useState } from "react";
import "./web-index.less";
import {
  CHAT_MENU_ID,
  KNOWLEDGE_MENU_ID,
  LOGIN_MENU_ID,
  navButtons,
  NOTE_MENU_ID,
  OCR_MENU_ID,
  PROMPT_MENU_ID,
  QUESTION_MENU_ID,
  SETUP_MENU_ID,
  TOOL_MENU_ID,
  WRITER_MENU_ID,
} from "@/config/menu";
import IconFont from "@/components/IconFont";
import { Button, Flex, Layout, theme, Tooltip, Typography } from "antd";
import Chat from "@/entrypoints/sidepanel/pages/chat/index";
import Writer from "@/entrypoints/sidepanel/pages/writer/index";
import Question from "@/entrypoints/sidepanel/pages/question/index";
import OCR from "@/entrypoints/sidepanel/pages/ocr/index";
import Prompt from "@/entrypoints/sidepanel/pages/prompt/index";
import Mine from "@/entrypoints/sidepanel/pages/mine/index";
import Knowledge from "@/entrypoints/sidepanel/pages/knowledge";
import KnowledgeNew from "@/entrypoints/sidepanel/pages/knowledgenew";
import Tool from "@/entrypoints/sidepanel/pages/tool";
import { SHADOW_SIDE_PANEL, SIDE_PANEL_WRAPPER_ID } from "@/entrypoints/content/sidepanel";
import Note from "@/entrypoints/sidepanel/pages/note/index";
import setModifyItem, { MENU_NAME_STORAGE_KEY } from "@/utils/browserStorageCurrentPage";
import { customToken } from "@/theme.json";
import classNames from "classnames";
import { LayoutOutlined, SettingOutlined } from "@ant-design/icons";
import { usePermissions } from "../PermissionProvider";
import UpdateMask from "@/components/Update";
import Login from "./login";
import { getRefereshToken, getTenantId } from "@/utils/auth.ts";

const { Content, Sider } = Layout;

export const SIDE_PANEL_CONTAINER_WEB = "side-panel-container-web";
const { useToken } = theme;
const SidePanelLayout: React.FC = () => {
  const { permissions, userInfo, setPermissions, setUserInfo, setPoint } = usePermissions();
  const [selected, setSelected] = useState(navButtons[0]);
  const [currentId, setCurrentId] = useState<number | string>(0);
  const [defaultNote, setDefaultNote] = useState(null);
  const [knowledgeId, setKnowledgeId] = useState<string>("");
  const { token } = useToken();
  const fetchRequest = useFetchRequest();
  const isShow = import.meta.env.VITE_WEB_URL;

  useEffect(() => {
    browser.storage.local.get(["isImgType"]).then((result) => {
      if (result.isImgType) {
        setSelected(navButtons[OCR_MENU_ID]);
        browser.storage.local.remove("isImgType");
      }
    });
    browser.runtime.onMessage.addListener(openListen);
    return () => {
      browser.runtime.onMessage.removeListener(openListen);
    };
  }, []);
  // 鼠标摁下
  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement>) => {
    const shadowDom = document.getElementById(SHADOW_SIDE_PANEL).shadowRoot;
    const div = shadowDom.querySelector<HTMLDivElement>(`#${SIDE_PANEL_WRAPPER_ID}`);
    const startX = e.clientX;
    const startWidth = div.offsetWidth;
    const onMouseMove = (e) => {
      const newWidth = startWidth + (startX - e.clientX);
      // 限制宽度最大为 800px，最小为 400px
      if (newWidth >= 400 && newWidth <= 800) {
        browser.storage.local.set({ siderWidth: newWidth });
        div.style.width = newWidth + "px";
      }
    };

    const onMouseUp = () => {
      document.removeEventListener("mousemove", onMouseMove);
      document.removeEventListener("mouseup", onMouseUp);
    };
    document.addEventListener("mousemove", onMouseMove);
    document.addEventListener("mouseup", onMouseUp);
  };
  useEffect(() => {
    // 监听来自background的消息，处理新标签页创建
    const handleBackgroundMessages = (message) => {
      console.log(message, 78781);
      if (message.type === "tabCreated") {
        // 如果当前已经打开了标签页选择面板，则更新标签页列表
        const newTab = message.tab;
        newTab.libName = newTab.title.replace(/[\r\n]/g, "");
        console.log(message);
        newTab.favIconUrl = newTab.favIconUrl || browser.runtime.getURL("/images/ico.png");
        browser.storage.local.get(["settingInfo"]).then((result) => {
          console.log(result, 999991);
          if (result?.settingInfo?.defaultStorage) {
            fetchRequest({
              api: "clientCrawSave",
              params: {
                url: message?.tab?.url,
                title: message?.tab?.title || message?.tab?.libName,
              },
              callback(resource) {
                if (resource.code === 200) {
                  console.log("存入成功！");
                }
              },
            });
          }
        });
      }
    };
    browser.runtime.onMessage.addListener(handleBackgroundMessages);
    return () => {
      browser.runtime.onMessage.removeListener(handleBackgroundMessages);
    };
  }, []);

  // 组件注册时，便开始监听用户自身便签数据的变化，一旦变化，重新渲染列表
  useEffect(() => {
    // changes 是一个对象，它代表了在 browser.storage.local 中发生的变化。
    // 具体来说，当存储区中的数据变化（例如，添加、修改或删除某个值）时，handleNoteListChanged 函数会被调用，
    // 并且变化的内容将作为参数 changes 传入。
    const handleNoteListChanged = async (changes) => {
      let sinoKey = sessionStorage.getItem("sino-tap-key");
      let changesValue = changes[NOTE_DETAIL_STORAGE_KEY + sinoKey];
      if (changesValue && changesValue.newValue) {
        setDefaultNote(changesValue.newValue || {});
        browser.storage.local.remove([NOTE_DETAIL_STORAGE_KEY + sinoKey]);
        handleClickTool(navButtons[Number(NOTE_MENU_ID)]);
      }
    };
    browser.storage.local.onChanged.addListener(handleNoteListChanged);
    return () => {
      browser.storage.local.onChanged.removeListener(handleNoteListChanged);
    };
  }, []);
  const handleNavigate = (type) => {
    setSelected(navButtons[type]);
  };
  const handleNavigateToMine = (resourceList: any[], user: UserInfo) => {
    if (resourceList.length > 0) {
      let index = navButtons.findIndex((x) => resourceList.includes(x.permissions));
      if (index !== -1) {
        setUserInfo(user);
        setSelected(navButtons[index]);
        setCurrentId(navButtons[index].id);
        setKnowledgeId("");
      }
    }
  };
  const openListen = (message) => {
    if (message.extractImg64) {
      setSelected(navButtons[OCR_MENU_ID]);
    }
  };

  const handleClickTool = (item: any) => {
    verifyPermissions();
    setModifyItem(MENU_NAME_STORAGE_KEY, {
      key: new Date().getTime(),
      value: "",
    });
    if (item.id == SETUP_MENU_ID) {
      window.open(browser.runtime.getURL("/options.html"), "_blank");
    } else {
      setCurrentId(item.id);
      setKnowledgeId("");
      setSelected(item);
    }
  };
  const siderStyle: React.CSSProperties = {
    height: "100vh",
    position: "fixed",
    top: 0,
    bottom: 0,
    right: 0,
    width: 60,
    background: customToken.background,
    scrollbarWidth: "thin",
    scrollbarColor: "unset",
  };

  const verifyPermissions = () => {
    if (!userInfo?.id) {
      setSelected(navButtons[LOGIN_MENU_ID]);
    }
  };

  useEffect(() => {
    console.log("web-index监听用户信息", userInfo);
    verifyPermissions();
  }, [userInfo]);

  useEffect(() => {
    console.log("web-index监听权限变更", permissions, userInfo);
    handleNavigateToMine(permissions, userInfo);
  }, [permissions]);

  // 权限更新，重新获取权限
  const getResourceData = () => {
    fetchRequest({
      api: "getResource",
      params: {},
      callback(resource) {
        if (resource || resource.code === 200) {
          setPermissions(resource.data.resourceList);
          cacheSet("permissions", JSON.stringify(resource.data.resourceList));
        }
      },
    });
  };
  // 监听background.js发送的消息，处理退出登录
  const handleSubscribe = async function (message) {
    console.log("监听background.js发送的消息，处理退出登录", message);
    switch (message.type) {
      case "mqttAndLogout": {
        cacheSet("userInfo", "");
        browser.runtime.sendMessage({ type: "updateOptions" });
        // 插件端清除
        browser.runtime.sendMessage({ ChromeClearCookie: true });
        // 网页端清除
        document.cookie = `url=https://scrm.sino-bridge.com:8098; userInfo={}`;
        browser.storage.local.remove("tenantId");
        browser.storage.local.remove("token");
        browser.storage.local.remove("permissions");
        browser.storage.local.remove("refreshTokenKey");
        break;
      }
      case "mqttResourceChange": {
        console.log("权限更新，重新获取权限");
        getResourceData();
        break;
      }
      case "mqttPointChange": {
        const point = {
          effectivePoint: message.point,
          totalPoint: message.totalPoint,
        };
        cacheSet("point", JSON.stringify(point));
        break;
      }
      case "openChat": {
        // 打开聊天 历史聊天记录同步划词ai回复的
        setCurrentId(CHAT_MENU_ID);
        setKnowledgeId("");
        if (message.data) {
          browser.storage.local.set({
            characterData: { id: message.data.currentConversation, agentId: message.data.agentId, time: Date.now() },
          });
        } else {
          browser.storage.local.set({
            characterDataTime: { time: Date.now() },
          });
        }

        break;
      }
      default:
        break;
    }
  };
  // 监听background.js发送的消息
  useEffect(() => {
    getResourceData();
    browser.runtime.onMessage.addListener(handleSubscribe);
    return () => {
      browser.runtime.onMessage.removeListener(handleSubscribe);
    };
  }, []);

  const toChat = (data) => {
    console.log("-------data", data);
    setCurrentId(CHAT_MENU_ID);
    setKnowledgeId(data);
  };

  const topStyle: React.CSSProperties = {
    position: "absolute",
    left: "-22.9px",
    top: "-30.41px",
    width: "200px",
    height: "200px",
    background: "linear-gradient(180deg, rgba(189, 225, 255, 0.4) 0%, rgba(224, 242, 255, 0) 100%)",
    filter: "blur(54.4px)",
    zIndex: 1,
    pointerEvents: "none",
  };
  return (
    <div className="side-panel-container" id={SIDE_PANEL_CONTAINER_WEB}>
      <UpdateMask />
      <Flex className="c-resize" align="center" justify="center" onMouseDown={handleMouseDown}>
        <span className="arrow left">&larr;</span>
        <span className="arrow right">&rarr;</span>
      </Flex>
      {userInfo?.id ? (
        <Layout hasSider className="side-panel-layout">
          <Content className="content-info">
            <Flex className="side-panel-content" vertical>
              <Flex className="side-panel-route">
                <Content style={topStyle}></Content>
                {currentId == CHAT_MENU_ID && <Chat knowledgeId={knowledgeId} />}
                {currentId == WRITER_MENU_ID && <Writer />}
                {currentId == QUESTION_MENU_ID && <KnowledgeNew toChat={toChat} />}
                {/* {currentId == QUESTION_MENU_ID && <Question />} */}
                {currentId == OCR_MENU_ID && <OCR />}
                {currentId == NOTE_MENU_ID && (
                  <Note defaultNote={defaultNote} handleNavigate={() => handleNavigate(NOTE_MENU_ID)} />
                )}
                {currentId == PROMPT_MENU_ID && <Prompt />}
                {currentId == LOGIN_MENU_ID && <Mine />}
                {currentId == KNOWLEDGE_MENU_ID && <Knowledge toChat={toChat} />}
                {currentId == TOOL_MENU_ID && <Tool />}
              </Flex>
            </Flex>
          </Content>
          <Sider style={siderStyle} width={60}>
            <Flex vertical justify="space-between" style={{ height: "100%" }}>
              <Flex vertical className={`layout-menu-func ${isShow ? "layout-menu-func-web" : ""}`}>
                {isShow ? (
                  <Tooltip
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                    placement="bottom"
                    title="整页聊天"
                  >
                    <Flex
                      vertical
                      justify="center"
                      className="layout-menu-div"
                      onClick={async () => {
                        const refreshToken = await getRefereshToken();
                        const tenantId = await getTenantId();
                        const url = new URL(import.meta.env.VITE_WEB_URL);
                        url.searchParams.set("refreshToken", refreshToken);
                        url.searchParams.set("tenantId", tenantId);
                        window.open(url.toString(), "_blank");
                        // window.open(import.meta.env.VITE_WEB_URL, "_blank");
                      }}
                    >
                      <Flex className="layout-icon" justify="center" align="center">
                        <Button
                          className="btn-icon"
                          type="text"
                          size="small"
                          icon={<LayoutOutlined style={{ fontSize: "14px" }} />}
                        />
                      </Flex>
                    </Flex>
                  </Tooltip>
                ) : (
                  <Flex style={{ opacity: 0, width: 0, height: 0, overflow: "hidden" }}>占位不展示</Flex>
                )}
                {navButtons
                  .filter((tool) => permissions.includes(tool.permissions) || tool.permissions == "")
                  .map((tool) => {
                    return (
                      tool.id != LOGIN_MENU_ID &&
                      tool.id != SETUP_MENU_ID && (
                        <Tooltip
                          getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                          placement="left"
                          title={tool.title}
                          key={tool.id}
                        >
                          <Flex
                            vertical
                            justify="center"
                            className="layout-menu-div"
                            onClick={() => handleClickTool(tool)}
                          >
                            <Flex className="layout-icon" justify="center" align="center">
                              <Button
                                className={classNames("btn-icon", { "btn-icon-active": tool.id == currentId })}
                                type="text"
                                icon={
                                  <IconFont
                                    className="icon"
                                    type={tool.id != currentId ? tool.unselectedImg : tool.selectedImg}
                                    isGradien={tool.id == currentId}
                                    fill={tool.id != currentId ? token.colorText : ""}
                                  />
                                }
                              />
                            </Flex>
                            <Typography.Text
                              className={classNames("layout-text", { "layout-text-active": tool.id == currentId })}
                            >
                              {tool.title}
                            </Typography.Text>
                          </Flex>
                        </Tooltip>
                      )
                    );
                  })}
              </Flex>
              <Flex vertical justify="space-between">
                {navButtons
                  .filter((tool) => permissions.includes(tool.permissions) || tool.permissions == "")
                  .map((tool) => {
                    return (
                      (tool.id == LOGIN_MENU_ID || tool.id == SETUP_MENU_ID) && (
                        <Tooltip
                          getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                          placement="left"
                          title={tool.title}
                          key={tool.id}
                        >
                          <Flex
                            className="layout-icon layout-icon-seting"
                            justify="center"
                            align="center"
                            onClick={() => handleClickTool(tool)}
                          >
                            {tool.id == LOGIN_MENU_ID && (
                              <Button
                                className={classNames("btn-icon", { "btn-icon-active": tool.id == currentId })}
                                type="text"
                                icon={
                                  <IconFont
                                    type={tool.id != currentId ? tool.unselectedImg : tool.selectedImg}
                                    className="icon"
                                    isGradien={tool.id == currentId}
                                    fill={tool.id != currentId ? token.colorText : ""}
                                  />
                                }
                              />
                            )}
                            {tool.id == SETUP_MENU_ID && (
                              <Button
                                className={classNames("btn-icon", { "btn-icon-active": tool.id == currentId })}
                                type="text"
                                icon={
                                  <SettingOutlined
                                    className="icon"
                                    style={{
                                      color: tool.id != currentId ? token.colorText : "#1888ff",
                                    }}
                                  />
                                }
                              />
                            )}
                          </Flex>
                        </Tooltip>
                      )
                    );
                  })}
              </Flex>
            </Flex>
          </Sider>
        </Layout>
      ) : (
        <Flex justify="center" align="center" style={{ height: "100%", width: "100%" }}>
          <Login handleNavigate={handleNavigateToMine} />
        </Flex>
      )}
    </div>
  );
};

export default SidePanelLayout;
