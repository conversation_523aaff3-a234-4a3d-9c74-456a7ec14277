import React, { useState } from "react";
import { Button, Checkbox, Flex, Popconfirm, Tag, Tooltip, Upload, message, theme } from "antd";
import {
  FileOutlined,
  SaveOutlined,
  EditOutlined,
  UploadOutlined,
  DeleteOutlined,
  TagFilled,
  TagOutlined,
} from "@ant-design/icons";
import classnames from "classnames";
import type { MenuProps, UploadProps } from "antd";
import "./index.less";
import IconFont from "@/components/IconFont";

type Props = {
  data: {
    id: string;
    libName: string;
    docCount: number;
    allFileSize: string;
    isCollect?: string;
    createBy?: string;
    createBy_text?: string;
  };
  currentUserInfo: any; // 当前用户信息
  knowledgeType?: string; // 当前tab栏切换 1最近 3企业 2 项目  4 个人
  checked?: boolean;
  onCheckChange?: (checked: boolean, value: any, type: string) => void;
  onDelete?: (item: Props["data"], type: number) => void;
  setParentLoading?: (loading: boolean) => void;
  onEdit?: (item: Props["data"]) => void;
  onShare?: (item: Props["data"]) => void;
  onCommon?: (item: Props["data"]) => void;
};
const { useToken } = theme;
const KnowledgeCard: React.FC<Props> = ({
  data,
  knowledgeType,
  checked = false,
  currentUserInfo,
  onCheckChange,
  setParentLoading,
  onEdit,
  onShare,
  onDelete,
  onCommon,
}) => {
  const { token } = useToken();
  const fetchRequest = useFetchRequest();
  const uploadProps: UploadProps = {
    name: "file",
    multiple: true,
    showUploadList: false,
    beforeUpload(file) {
      if (file.size <= 0) {
        message.open({
          type: "error",
          content: "上传文件的大小必须大于0！",
        });
        return false;
      }
    },
  };
  // 文件转base64
  function fileToBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();

      // 成功读取文件时的回调
      reader.onload = () => {
        resolve(reader.result); // Base64 编码的字符串
      };

      // 读取文件失败时的回调
      reader.onerror = (error) => {
        reject(error);
      };

      // 读取文件并转为 Base64
      reader.readAsDataURL(file);
    });
  }

  const handleCustomRequest = async (options) => {
    setParentLoading(true);
    const { file } = options;
    const formData = {
      fileStr: await fileToBase64(file),
      fileName: file.name,
      baseId: data.id,
    };
    fetchRequest({
      api: "uploadKnowledgeFile",
      params: formData,
      file: true,
      callback: async (res) => {
        if (res.code === 200) {
          message.open({
            type: "success",
            content: "操作成功",
          });
        } else {
          message.open({
            type: "error",
            content: "上传失败",
          });
        }
        setParentLoading(false);
      },
    });
  };
  // 添加常用
  const addLibUserRela = () => {
    setParentLoading(true);
    fetchRequest({
      api: "setLibUserRela",
      params: { libId: data.id, relaType: "common" },
      callback: (res) => {
        if (res.code === 200) {
          onCommon?.(data);
          message.open({
            type: "success",
            content: "设置成功",
          });
        } else {
          message.open({
            type: "error",
            content: "设置失败",
          });
        }
        setParentLoading(false);
      },
    });
  };

  // 删除常用
  const delLibUserRela = () => {
    setParentLoading(true);
    fetchRequest({
      api: "delCommon",
      params: data.id,
      callback: (res) => {
        if (res.code === 200) {
          onCommon?.(data);
          message.open({
            type: "success",
            content: "取消成功",
          });
        } else {
          message.open({
            type: "error",
            content: "取消失败",
          });
        }
        setParentLoading(false);
      },
    });
  };

  return (
    <Flex className="knowledge-card" vertical gap={token.marginXXS}>
      <Flex justify="space-between">
        <Flex align="center" gap={token.marginXXS}>
          <Button
            className="btn-knowledge-icon"
            type="text"
            size="small"
            style={
              knowledgeType === "2"
                ? { backgroundColor: token.volcano1 }
                : knowledgeType === "3"
                  ? { backgroundColor: token.geekblue1 }
                  : knowledgeType === "4"
                    ? { backgroundColor: token.cyan1 }
                    : {}
            }
            icon={
              <IconFont
                className="icon"
                type="knowledgeBaseOutlined"
                fill={
                  knowledgeType == "2"
                    ? token.volcano6
                    : knowledgeType == "3"
                      ? token.geekblue6
                      : knowledgeType == "4"
                        ? token.cyan6
                        : undefined
                }
              />
            }
          />
          {data?.isCollect == "1" && (
            <Tag color="#FFFBE6" style={{ color: "#FAAD14" }}>
              常用
            </Tag>
          )}
        </Flex>
        <Checkbox
          value={data.id}
          style={{
            opacity: checked ? 1 : 0,
            transition: "opacity 0.2s",
          }}
          className="knowledge-checkbox"
          checked={checked}
          onClick={(e) => e.stopPropagation()}
          onChange={(e) => onCheckChange?.(e.target.checked, data, "1")}
        />
      </Flex>

      <Flex className="knowledge-name">{data.libName}</Flex>

      <Flex
        gap={8}
        align="center"
        className="knowledge-card-opeate"
        // className={classnames("knowledge-card-opeate", {
        //   "show-always": knowledgeType === "1",
        // })}
      >
        <Flex
          className="knowledge-card-info"
          style={{
            color: token.colorTextQuaternary,
            fontSize: token.fontSizeSM,
            lineHeight: "24px",
          }}
          align="center"
        >
          {knowledgeType == "2" && (
            <Tag
              bordered={false}
              style={{ borderRadius: "50%", marginRight: "4px", color: token.colorTextTertiary, fontSize: "10px" }}
            >
              {currentUserInfo?.id == data?.createBy ? "我" : data?.createBy_text?.charAt(0)}
            </Tag>
          )}
          <FileOutlined />
          <span style={{ marginLeft: "2px", marginRight: token.marginXXS }}>{data.docCount || 0}</span>
          <SaveOutlined style={{ marginLeft: token.marginXXS }} />
          <span style={{ marginLeft: "2px" }}>{(data?.allFileSize ?? 0) + "MB"}</span>
        </Flex>

        <Flex className="knowledge-opeate">
          {knowledgeType != "3" && (
            <Tooltip placement="top" title="上传" getPopupContainer={(triggerNode) => triggerNode.parentNode as any}>
              <Upload
                {...uploadProps}
                maxCount={5}
                customRequest={handleCustomRequest}
                onClick={(e) => e.stopPropagation()}
              >
                <Button
                  icon={<UploadOutlined className="know-edit-icon know-upload-icon" />}
                  type="text"
                  size="small"
                ></Button>
              </Upload>
            </Tooltip>
          )}
          {knowledgeType != "3" && currentUserInfo?.id == data?.createBy && (
            <>
              <Tooltip placement="top" title="分享" getPopupContainer={(node) => node.parentNode as any}>
                <Button
                  type="text"
                  size="small"
                  onClick={(e) => {
                    e.stopPropagation();
                    onShare?.(data);
                  }}
                  icon={<IconFont type="Forward" />}
                />
              </Tooltip>
              <Tooltip placement="top" title="编辑" getPopupContainer={(triggerNode) => triggerNode.parentNode as any}>
                <Button
                  icon={<EditOutlined className="know-edit-icon " />}
                  onClick={(e) => {
                    e.stopPropagation();
                    onEdit?.(data);
                  }}
                  type="text"
                  size="small"
                ></Button>
              </Tooltip>
              <Popconfirm
                title={
                  <div style={{ maxWidth: 200, whiteSpace: "normal" }}>
                    {knowledgeType == "2"
                      ? "该知识库如果已协同给他人，删除后所有人将不可见，也不可恢复，确认要删除？"
                      : "确认要删除该知识库吗？"}
                  </div>
                }
                onConfirm={(e) => {
                  e?.stopPropagation(); // 阻止冒泡
                  onDelete?.(data, 1);
                }}
                getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                okText="删除"
                onCancel={(e) => {
                  e?.stopPropagation(); // ✅ 阻止“取消”按钮的冒泡
                }}
                cancelText="取消"
              >
                <Tooltip
                  placement="top"
                  title="删除"
                  getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                >
                  <Button
                    onClick={(e) => e.stopPropagation()}
                    icon={<DeleteOutlined className="know-edit-icon" />}
                    type="text"
                    size="small"
                  ></Button>
                </Tooltip>
              </Popconfirm>
            </>
          )}
          {knowledgeType == "3" && (
            <>
              {data?.isCollect == "1" ? (
                <Tooltip placement="top" title="取消常用" getPopupContainer={(node) => node.parentNode as any}>
                  <Button
                    type="text"
                    size="small"
                    icon={
                      <TagFilled
                        style={{ color: token.gold6 }}
                        onClick={(e) => {
                          e.stopPropagation();
                          delLibUserRela();
                        }}
                      />
                    }
                  />
                </Tooltip>
              ) : (
                <Tooltip placement="top" title="设为常用" getPopupContainer={(node) => node.parentNode as any}>
                  <Button
                    type="text"
                    size="small"
                    icon={<TagOutlined />}
                    onClick={(e) => {
                      e.stopPropagation();
                      addLibUserRela();
                    }}
                  />
                </Tooltip>
              )}
            </>
          )}
        </Flex>
      </Flex>
    </Flex>
  );
};

export default KnowledgeCard;
