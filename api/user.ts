/** 用户 API */
import request from "@/api";
import { getTenantId } from "@/utils/auth.ts";

const API_BASE_PUB: string = import.meta.env["VITE_API_BASE_PUB"] || "";
const API_BASE_SYS: string = import.meta.env["VITE_API_BASE_SYS"] || "";
const API_BASE_INS: string = import.meta.env["VITE_API_BASE_INS"] || "";
const API_BASE_BASE: string = import.meta.env["VITE_API_BASE_BASE"] || "";

/** 通过用户账号密码登录接口 */
export function loginByAccount(params: LoginByAccountRequest): Promise<any> {
  return request({
    url: `${API_BASE_PUB}/wecom/user/getUserIdentityByAccount`,
    method: "POST",
    params,
  });
}

/** 通过refreshToken获取token */
export function refreshGetToken(params: RefreshGetTokenRequest): Promise<any> {
  return request({
    url: `/oauth/anyTenant/refresh`,
    method: "POST",
    params,
    baseUrl: import.meta.env["VITE_USERINFO_BASS"],
  });
}

/** 通过token换取一个新的token和refreshToken */
export function switchTenantAndOrg(params: any): Promise<any> {
  return request({
    url: `/oauth/anyone/switchTenantAndOrg`,
    method: "PUT",
    params,
    baseUrl: import.meta.env["VITE_USERINFO_BASS"],
  });
}

/** 获取当前用户信息 */
export function getCurrentUserInfo(params: GetUserInfoByIdRequest): Promise<any> {
  return request({
    url: `/oauth/anyone/getUserInfoById`,
    method: "GET",
    baseUrl: import.meta.env["VITE_USERINFO_BASS"],
  });
}

/** 退出登录 */
export function logout(): Promise<any> {
  return request({
    url: `/oauth/anyUser/logoutAll`,
    method: "POST",
    baseUrl: import.meta.env["VITE_USERINFO_BASS"],
  });
}

/** 修改用户头像 */
export function editUserAvatar(data: EditUserAvatarRequest): Promise<any> {
  return request({
    url: `${API_BASE_SYS}/sys/user/profile/avatar`,
    method: "POST",
    data,
  });
}

/** 修改密码信息 */
export function editPassword(data: EditPasswordRequest): Promise<any> {
  return request({
    url: `/oauth/anyone/password`,
    method: "PUT",
    baseUrl: import.meta.env["VITE_USERINFO_BASS"],
    data,
  });
}

/** 修改用户信息 */
export function editUserInfo(data: EditUserInfoRequest): Promise<any> {
  return request({
    url: `/oauth/anyone/baseInfo`,
    method: "PUT",
    baseUrl: import.meta.env["VITE_USERINFO_BASS"],
    data,
  });
}

// 获取用户信息 FIXME 暂时没用到，该接口需要替换掉现在的getCurrentUserInfo接口
export function profileInfo(): Promise<any> {
  return request({
    url: `${API_BASE_SYS}/sys/user/profile/info`,
    method: "POST",
  });
}

/** 获取当前用户所有资源 */
export async function getResource(): Promise<any> {
  const tenantId = await getTenantId();
  return request({
    url: `/oauth/anyone/visible/resource`,
    method: "GET",
    headers: {
      TenantId: tenantId,
    },
    baseUrl: import.meta.env["VITE_USERINFO_BASS"],
  });
}

export function getLatestVersion(params: any): Promise<any> {
  return request({
    url: `${API_BASE_INS}/versionManage/client/latestVersion`,
    method: "GET",
    params,
  });
}

// 获取当前租户下用户信息
export function getBaseEmployee(params: any): Promise<any> {
  return request({
    url: `/base/baseEmployee/findAll`,
    method: "GET",
    params,
    baseUrl: import.meta.env["VITE_USERINFO_BASS_API_BASE"],
  });
}
