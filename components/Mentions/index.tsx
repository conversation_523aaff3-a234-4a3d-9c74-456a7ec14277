import React, { use<PERSON>allback, useEffect, useRef, useState, forwardRef, useImperativeHandle } from "react";
import type { UploadProps } from "antd";
import { RcFile } from "antd/es/upload";
import { OCRData, OCRProgress } from "@/types/ocr";
import "./index.less";
import {
  Button,
  Divider,
  Mentions,
  Dropdown,
  Flex,
  message,
  Space,
  Spin,
  theme,
  Tooltip,
  Upload,
  Input,
  Popover,
} from "antd";
import { knowdgeSVGIcon } from "@/config/menu/knowdge";
import IconFont from "@/components/IconFont";
import { useGetState } from "ahooks";
import { SHADOW_SIDE_PANEL } from "@/entrypoints/content/sidepanel";
import { getToken } from "@/utils/auth.ts";
import Knowledge from "@/entrypoints/sidepanel/pages/chat/components/knowledge";
import WebPageFilechild from "@/entrypoints/sidepanel/pages/chat/components/webPageFilechild/index.tsx";
import classNames from "classnames";
import { AIType } from "@/config/options/agent";
import { sanitizeFilename } from "@/utils/image";
import { usePermissions } from "@/entrypoints/sidepanel/components/PermissionProvider";
import { debounce } from "@/utils/debounce";
import { useFetchRequest } from "@/hooks/useFetchRequest";
import useClipboard from "@/hooks/useClipboard";
import { imageToBase64, dataURLtoFile } from "@/utils/image";
import { crop } from "@/utils/crop";
import {
  CloseCircleFilled,
  DatabaseOutlined,
  EnterOutlined,
  FolderAddOutlined,
  FolderOutlined,
  LayoutOutlined,
  LeftOutlined,
  PaperClipOutlined,
  RightOutlined,
  ScissorOutlined,
} from "@ant-design/icons";
const searchParamsInit = {
  pageNum: 1,
  pageSize: 3000,
  entity: {
    libName: "",
  },
};
const { useToken } = theme;
interface MentionsComponentProps {
  agentId: string;
  onQueryChange?: (query: string) => void;
}
export interface MentionsComponentRef {
  getMentionsData: () => {};
  setMentionsData: (data: any) => {};
}
const useOCRUpload = (agentId: string, setQueryCallback?: (text: string) => void) => {
  const initialData: OCRData = {
    image: "",
    texts: [],
  };
  const [progress, setProgress] = useState<OCRProgress>(1);
  const { userInfo } = usePermissions();
  const [currentData, setCurrentData] = useState<OCRData>(initialData);
  const [imageList, setImageList] = useState([]);
  const clipboard = useClipboard();
  const fetchRequest = useFetchRequest();
  const removeBase64ImageHeader = (base64String: string) => {
    // 分割字符串，并去掉前两个分割后的部分
    const parts = base64String.split(",");
    const cleanBase64 = parts.length > 1 ? parts.slice(1).join(",") : base64String;
    // URL编码
    const urlEncoded = encodeURIComponent(cleanBase64);
    return urlEncoded;
  };
  const uploadProps: UploadProps = {
    accept: "image/png, image/jpeg, image/webp, image/gif",
    action: "",
    multiple: false,
    style: {},
    showUploadList: false,
    beforeUpload(fileObj) {
      clipboard.setCopied(false);
      const isLt2M = fileObj.size / 1024 / 1024 < 5; // 限制文件大小为5MB
      if (!isLt2M) {
        message.error("不允许超过5MB!");
        return isLt2M; // 返回 false 则会阻止文件上传
      }

      const isValidType = /\.(png|jpg|jpeg|webp|gif)$/.test(fileObj.name);
      if (!isValidType && fileObj.name != "截图文件") {
        message.error("仅支持上传 PNG、JPEG、WEBP 或 GIF 格式的文件！");
        return;
      }
      try {
        const file = new File([fileObj as Blob], fileObj.name, { type: fileObj.type });
        imageToBase64(file).then(async (imageBase64: string) => {
          const fileData = {
            fileName: fileObj.name,
            fileStr: imageBase64,
            loading: true,
            path: "/files/upload",
            agentId: agentId,
            user: userInfo?.id,
          };
          fetchRequest({
            api: "uploadChatFile",
            params: fileData,
            file: true,
            callback: async (response) => {
              if (response.code == "200") {
                setImageList([response.data]);
              }
            },
          });
          setCurrentData({ image: imageBase64, texts: [] });
          let image = removeBase64ImageHeader(imageBase64);
          const ocrConfig = {
            image: image,
            language_type: "CHN_ENG",
            detect_direction: true,
            detect_language: false,
            paragraph: false,
            probability: false,
          };
          fetchRequest({
            api: "baiduocr",
            params: ocrConfig,
            callback: (ocrResult) => {
              if (ocrResult.code == "200") {
                setProgress(4);
                const texts = ocrResult?.data?.result?.texts as Array<{ text: string }>;
                setCurrentData({
                  image: imageBase64,
                  texts: texts,
                });

                // 将OCR识别的文本内容赋值给input框
                if (texts && texts.length > 0 && setQueryCallback) {
                  const recognizedText = texts.map((item) => item.text).join("\n");
                  setQueryCallback(recognizedText);
                }
              } else {
                message.open({
                  type: "error",
                  content: ocrResult.msg,
                });
              }
            },
          });
        });
      } catch (e) {
        console.debug("上传失败");
        setCurrentData({ image: "", texts: [] });

        setProgress(5);
      }
    },
  };

  return {
    progress,
    setProgress,
    imageList,
    setImageList,
    currentData,
    setCurrentData,
    uploadProps,
    clipboard,
  };
};
const MentionsComponent = forwardRef<MentionsComponentRef, MentionsComponentProps>(
  ({ agentId, onQueryChange }, ref) => {
    const { token: csstoken } = useToken();
    const [ocrCon, setOcrCon] = useState<string>(""); // ocr的文本的文本
    const { permissions, setPermissions, userInfo, point } = usePermissions();
    const { currentData, imageList, setImageList, uploadProps, setProgress, setCurrentData } = useOCRUpload(
      agentId,
      setOcrCon,
    );
    const [promptList, setPromptList] = useState<any>([]); // 下拉框提示词数据
    const [knowledModel, setknowledModel] = useState<boolean>(false);
    const [konwTooltipOpen, setKonwTooltipOpen] = useState(false);
    const [localFile, setLocalFile] = useState<any>([]);
    const [webPageFile, setWebPageFile] = useState<any>([]);
    const [allFile, setAllFile] = useState<any>([]);
    const [localWebPageFile, setLocalWebPageFile] = useState<any>([]);
    const [webPageFileModal, setWebPageFileModal] = useState<any>(false);
    const [cardData, setCardData] = useState<any>([]);
    const [dropdownOpen, setDropdownOpen] = useState(false);
    const [selectKnowledgeArr, setSelectKnowledgeArr] = useState<any>([]);
    const [query, setQuery, getQuery] = useGetState<string>("");
    const [oneWebPageArr, setOneWebPageArr] = useState<any>([]);
    const [aiList, setAiList] = useState<Array<AIType>>([]);
    const [tokenInfo, setTokenInfo] = useState<string>("");
    const [token, setToken] = useState<string>("");
    const [tags, setTags] = useState([]); // 选中的指令
    const popupContainerRef = useRef(null);
    const [dropdownVisible, setDropdownVisible] = useState(false); // 控制Dropdown显示
    const [promptData, setPromptData] = useState<any>([]); // 提示词整体数据
    const [currentAi, setCurrentAi] = useState<string>();
    const [templateFile, setTemplateFile] = useState([]); // 文件模板list
    const [quoteData, setQuoteData] = useState(""); // 划词选择的文本
    const [modalVisible, setModalVisible] = useState(false); // 知识库
    const [noteInfoData, setNoteInfoData] = useState<any>(); // 知识库弹框数据
    const [knowledLoading, setKnowledLoading] = useState<boolean>(false); // 知识库loading
    const [removedFiles, setRemovedFiles] = useState([]);
    const [allFileList, setAllFileList] = useState<number>(0);
    const [searchParams, setSearchParams] = useState(searchParamsInit);
    const [loading, setLoading] = useState<boolean>(false);
    // 是否显示左箭头
    const [isLeftShow, setIsLeftShow] = useState(false);
    // 是否显示右箭头
    const [isRightShow, setIsRightShow] = useState(false);
    /** 处理普通Web请求的hook */
    const fetchRequest = useFetchRequest();
    getToken().then((res) => {
      setToken(res);
    });
    const handleImg = async function (message: any) {
      if (message.extractImg64) {
        urlToBlob(message.extractImg64).then((blob) => {
          uploadProps.beforeUpload(blob as RcFile, []);
        });
      }

      switch (message.type) {
        case "captured": {
          let { left, top, width, height, href } = message;
          const base64 = await crop(href, { left, top, width, height });
          var file = dataURLtoFile(base64, "截图文件");
          uploadProps.beforeUpload(file as RcFile, []);
          break;
        }
        default:
          break;
      }
    };
    async function urlToBlob(url: string) {
      return fetch(url)
        .then((response) => response.blob())
        .then((blob) => blob);
    }

    // 初始化数据
    useEffect(() => {
      getAllListData(searchParams);
      chrome.storage.local.get(["extractImg64"], function (result) {
        if (result.extractImg64) {
          urlToBlob(result.extractImg64).then((blob) => {
            uploadProps.beforeUpload(blob as RcFile, []);
          });
        }
      });
      browser.runtime.onMessage.addListener(handleImg);
      return () => {
        browser.runtime.onMessage.removeListener(handleImg);
      };
    }, []);

    const uploadFile: UploadProps = {
      name: "file",
      multiple: true,
      headers: {
        [import.meta.env["VITE_API_HEADER_KEY"]]: token,
      },
      showUploadList: false,
      // accept: ".docx,.pptx,.xls,.xlsx,.csv,.txt,.pdf,.png,.gif,.jpg,.jpeg",
      accept: ".docx,.pptx,.xls,.xlsx,.csv,.txt,.pdf",
      beforeUpload(file) {
        if (localFile.length > 4) {
          message.error("最多上传5个附件");
          return Promise.reject(); // 返回拒绝的 Promise 阻止上传
        }
        const isLt2M = file.size / 1024 / 1024 < 15; // 限制文件大小为15MB
        if (!isLt2M) {
          message.error("不允许超过15MB!");
          return Promise.reject(); // 返回拒绝的 Promise 阻止上传
        }
        let arr = file.name.split(".");
        let fileName = arr[arr.length - 1] || "";
        // let fileFormat = ["docx", "pptx", "xls", "xlsx", "csv", "txt", "pdf", "gif", "jpeg", "png", "jpg"];
        let fileFormat = ["docx", "pptx", "xls", "xlsx", "csv", "txt", "pdf"];
        if (!fileFormat.includes(fileName)) {
          message.error("文件格式不正确!");
          return Promise.reject(); // 返回拒绝的 Promise 阻止上传
        }
      },
    };

    // 文件转base64
    function fileToBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();

        // 成功读取文件时的回调
        reader.onload = () => {
          resolve(reader.result); // Base64 编码的字符串
        };

        // 读取文件失败时的回调
        reader.onerror = (error) => {
          reject(error);
        };

        // 读取文件并转为 Base64
        reader.readAsDataURL(file);
      });
    }

    let uploadQueue: any[] = []; // 存储待上传的文件
    let isUploading = false; // 标记是否正在上传

    const processQueue = async () => {
      if (uploadQueue.length === 0) {
        isUploading = false; // 队列为空时，停止上传状态
        return;
      }

      isUploading = true;
      const { file, type } = uploadQueue.shift(); // 取出队列中的第一个任务
      await uploadFileNew(file, type); // 执行上传

      setTimeout(processQueue, 1000); // 1 秒后处理下一个任务
    };

    const handleCustomRequest = async (options: any) => {
      const { file } = options;
      uploadQueue.push({ file, type: 2 }); // 将文件任务加入队列

      if (!isUploading) {
        processQueue(); // 如果当前没有正在上传的任务，启动队列
      }
    };
    const uploadFileMaxCount = () => {
      return allFileList + (5 - localFile.length);
    };

    const items = [
      {
        key: "2",
        label: (
          <Flex align="center">
            <Flex className="tips">
              <DatabaseOutlined />{" "}
            </Flex>
            知识库
            <Flex className="upload-desc">选择询问的知识库</Flex>
          </Flex>
        ),
      },
      {
        key: "3",
        label: (
          <Upload
            {...uploadFile}
            maxCount={uploadFileMaxCount()}
            customRequest={handleCustomRequest}
            className="sino-upload-file"
          >
            <Flex className="upload-txt" align="center">
              <Flex className="tips">
                <FolderOutlined />
              </Flex>
              本地文件
              <Flex className="upload-desc">上传本地文件</Flex>
            </Flex>
          </Upload>
        ),
      },
      {
        key: "1",
        label: (
          <Flex align="center">
            <Flex className="tips">
              <LayoutOutlined />
            </Flex>
            从已打开的标签页选择
          </Flex>
        ),
      },
    ];
    const fileHandler = (item) => {
      if (item.key === "1") {
        setWebPageFileModal(true);
        bookHandler();
      } else if (item.key === "2") {
        //  知识库
        debounceNoteSearch(searchParams, selectKnowledgeArr);
        setknowledModel(true);
      }
    };
    const bookHandler = () => {
      chrome.runtime.sendMessage({ action: "getTabs" }, (response) => {
        if (response) {
          // 显示所有标签页的信息
          response.forEach((tab) => {
            tab.libName = tab.title.replace(/[\r\n]/g, "");
            tab.favIconUrl = tab.favIconUrl || browser.runtime.getURL("/images/ico.png");
          });
          let arrUrl = [];
          let arrInfo = [];
          for (let i = 0; i < response.length; i++) {
            for (let j = 0; j < webPageFile.length; j++) {
              if (webPageFile[j].id === response[i].id) {
                response[i].checked = webPageFile[i]?.checked || false;
              }
            }
            if (!arrUrl.includes(response[i].url)) {
              arrUrl.push(response[i].url);
              arrInfo.push(response[i]);
            }
          }
          setWebPageFile(arrInfo);
        }
      });
    };
    const getFavicon = async () => {
      let iconLink = "";
      const sendMessage = (message) => {
        return new Promise((resolve, reject) => {
          chrome.runtime.sendMessage(message, (response) => {
            if (chrome.runtime.lastError) {
              reject(chrome.runtime.lastError);
            } else {
              resolve(response);
            }
          });
        });
      };
      try {
        const response = await sendMessage({ action: "getTabs" });
        if (response) {
          // 处理标签页信息
          response.forEach((tab) => {
            tab.libName = tab.title.replace(/[\r\n]/g, "");
            if (tab.url == window.location.href) {
              iconLink = tab.favIconUrl || browser.runtime.getURL("/images/ico.png");
            }
          });
        }
      } catch (error) {
        console.error("Error:", error);
      }
      if (iconLink) {
        return iconLink;
      } else {
        return browser.runtime.getURL("/images/ico.png");
      }
    };

    // 上传文件
    let queue: Promise<void> = Promise.resolve(); // 初始化 Promise 队列
    const uploadFileNew = (file, type, data?) => {
      queue = queue.then(() => {
        return new Promise((resolve, reject) => {
          let fileData: any = {
            fileName: "",
            fileStr: "",
          };
          if (!file) {
            resolve();
            return;
          }

          // 使用 async/await 来处理异步逻辑
          (async () => {
            let sanitizeName = await sanitizeFilename(file.name);
            try {
              fileData = {
                fileName: sanitizeName,
                fileStr: await fileToBase64(file),
                loading: true,
                path: "/files/upload",
                agentId: agentId,
                user: userInfo?.id,
              };
              if (type == 1) {
                fileData.libName = data.libName;
                fileData.libDesc = data.libDesc;
                fileData.flag = "oneWebPageFile";
                fileData.faviocn = await getFavicon();
                setOneWebPageArr([...oneWebPageArr, fileData]);
              } else if (type == 2) {
                fileData.libName = sanitizeName;
                let arr = file.name.split(".");
                fileData.libDesc = arr[arr.length - 1];
                fileData.flag = "file";
                setLocalFile((prevFiles) => {
                  if (prevFiles.some((item) => item.libName === fileData.libName)) {
                    message.open({
                      type: "warning",
                      content: "已经添加了，不可重复添加",
                    });
                    return prevFiles;
                  }
                  return [...prevFiles, fileData];
                });
              } else if (type == 3) {
                let selectIdFilterArr = webPageFile
                  .filter(
                    (itemArr) =>
                      sanitizeName.substring(0, sanitizeName.lastIndexOf(".")) == sanitizeFilename(itemArr.title),
                  )
                  .map((itemArr) => ({
                    flag: "webPageFile",
                    libName: sanitizeFilename(itemArr.title),
                    libDesc: itemArr.url,
                    faviocn: itemArr.favIconUrl,
                    id: itemArr.id,
                    loading: true,
                  }));
                setLocalWebPageFile((prev) => [...prev, ...selectIdFilterArr]);
              }
            } catch (error) {
              console.error("文件转 Base64 出错：", error);
              reject(); // 发生错误时调用 reject
              return;
            }

            fetchRequest({
              api: "uploadChatFile",
              params: fileData,
              file: true,
              callback: async (response) => {
                const res = response.data;
                if (res?.name) {
                  if (type == 1) {
                    res.libName = data.libName;
                    res.libDesc = data.libDesc;
                    res.flag = "oneWebPageFile";
                    res.faviocn = await getFavicon();
                    setOneWebPageArr((prevArr) =>
                      prevArr.map((item) =>
                        item.libName === res.libName ? { ...item, ...res, loading: false } : item,
                      ),
                    );
                  } else if (type == 2) {
                    res.libName = res.name;
                    res.libDesc = res.extension;
                    res.flag = "file";
                    res.fileType = "document";
                    // const imgType = ["jpg", "png", "jpeg", "gif", "JPG", "PNG", "JPEG", "GIF"];
                    // if (imgType.includes(res.extension)) {
                    //   res.fileType = "image";
                    // } else {
                    //   res.fileType = "document";
                    // }
                    setLocalFile((prevArr) =>
                      prevArr.map((item) => (item.libName === res.name ? { ...item, ...res, loading: false } : item)),
                    );
                  } else if (type == 3) {
                    setLocalWebPageFile((prevArr) =>
                      prevArr.map((item) =>
                        item.libName == res.name.substring(0, res.name.lastIndexOf("."))
                          ? {
                              ...item,
                              loading: false,
                              uid: res.id,
                              size: res.size,
                              mime_type: res.mime_type,
                              extension: res.extension,
                            }
                          : item,
                      ),
                    );
                  }
                } else {
                  message.open({
                    type: "error",
                    content: "上传失败",
                  });
                }
                resolve(); // 上传完成后调用 resolve
              },
            });
          })();
        });
      });
    };

    const closeKnowledModelFalg = () => {
      setknowledModel(false);
    };
    const closeKnowledModel = (modalFalg, selectId = []) => {
      let selectIdArr = selectId;
      setknowledModel(modalFalg);

      let selectIdFilterArr = cardData
        .filter((itemArr) => selectIdArr.includes(itemArr.id)) // 过滤出符合条件的元素
        .map((itemArr) => ({
          id: itemArr.id,
          flag: "knowledge",
          libName: itemArr.libName,
          libDesc: itemArr.libDesc,
        }));
      setSelectKnowledgeArr(selectIdFilterArr);
    };
    // 这个是提交的
    const closeWebPageModel = (modalFalg, selectId = []) => {
      let selectIdArr = selectId;
      if (oneWebPageArr && setOneWebPageArr.length > 0) {
        if (selectIdArr && selectIdArr.length > 4) {
          message.open({
            type: "error",
            content: "最多可以选择4个",
          });
          return;
        }
      } else {
        if (selectIdArr && selectIdArr.length > 4) {
          message.open({
            type: "error",
            content: "最多可以选择5个",
          });
          return;
        }
      }
      setWebPageFileModal(false);
      chrome.runtime.sendMessage({ action: "getSelectedTabsContent", tabIds: selectIdArr }, (response) => {
        response.forEach((tabContent) => {
          const file = new File([tabContent.innerText?.innerText], tabContent.innerText?.title + ".txt", {
            type: "text/plain",
          });
          uploadFileNew(file, 3, selectIdArr);
        });
      });
    };
    const closeWebPageFalg = () => {
      setWebPageFileModal(false);
    };
    const handleCheckboxWebPageChange = (id, newChecked) => {
      const updatedItems = webPageFile.map((item) => (item.id === id ? { ...item, checked: newChecked } : item));
      setWebPageFile(updatedItems);
    };
    const keywordSearch = (value) => {
      const updateValue = { ...searchParams, entity: { libName: value } };
      setSearchParams(updateValue);
      setKnowledLoading(true);
      debounceNoteSearch(updateValue, selectKnowledgeArr);
    };
    const getAllListDataSearch = (updateSearchParams, latestSelectKnowledgeArr) => {
      getAllListData(updateSearchParams, latestSelectKnowledgeArr);
    };
    const debounceNoteSearch = useCallback(debounce(getAllListDataSearch, 500), []);

    const getAllListData = (searchParamsInit, latestSelectKnowledgeArr?) => {
      setKnowledLoading(true);
      fetchRequest({
        api: "getKnowledgeTeam",
        params: searchParamsInit,
        callback: (res) => {
          if (res.code === 200) {
            let data = res.data.records;
            for (let i = 0; i < data.length; i++) {
              // 检查当前项是否在已选择的知识库数组中
              if (latestSelectKnowledgeArr) {
                const matched = latestSelectKnowledgeArr.some((item) => item.id === data[i].id);
                data[i].checked = matched;
              } else {
                data[i].checked = false;
              }
            }
            setCardData(data);
          }

          setKnowledLoading(false);
        },
      });
    };
    const handleDeleteKnowledge = (itemObj, index) => {
      if (itemObj.flag === "knowledge") {
        setSelectKnowledgeArr(selectKnowledgeArr.filter((item, i) => item.id !== itemObj.id));

        setCardData(cardData.map((item) => (item.id === itemObj.id ? { ...item, checked: false } : item)));
      } else if (itemObj.flag === "file") {
        setRemovedFiles((prev) => [...prev, itemObj]);
        setLocalFile((prevFiles) => prevFiles.filter((f) => f.id !== itemObj.id));
      } else if (itemObj.flag === "webPageFile") {
        setLocalWebPageFile(localWebPageFile.filter((item, i) => item.id !== itemObj.id));
        for (let i = 0; i < webPageFile.length; i++) {
          if (webPageFile[i].id == itemObj.id) {
            webPageFile[i].checked = false;
          }
        }
        setWebPageFile(webPageFile);
      } else {
        const newOneWebPageArr = oneWebPageArr.filter((item, i) => item.id !== itemObj.id);
        setOneWebPageArr(newOneWebPageArr);
      }
    };
    const fileExtensionHandler = (item) => {
      if (item.libDesc === "pdf") {
        return <span className="extend-icon">{knowdgeSVGIcon.pdf}</span>;
      } else if (item.libDesc === "docx") {
        return <span className="extend-icon">{knowdgeSVGIcon.word}</span>;
      } else if (item.libDesc === "xls" || item.libDesc === "xlsx" || item.libDesc === "csv") {
        return <span className="extend-icon">{knowdgeSVGIcon.excel}</span>;
      } else if (item.libDesc === "txt") {
        return <span className="extend-icon">{knowdgeSVGIcon.txt}</span>;
      } else if (item.libDesc === "pptx") {
        return <span className="extend-icon">{knowdgeSVGIcon.ppt}</span>;
      }
    };
    const handleCheckboxChange = (id, newChecked) => {
      const updatedItems = cardData.map((item) => (item.id === id ? { ...item, checked: newChecked } : item));
      setCardData(updatedItems);
    };
    useEffect(() => {
      setAllFile([...localFile, ...selectKnowledgeArr, ...localWebPageFile, ...oneWebPageArr]);
    }, [localFile, selectKnowledgeArr, localWebPageFile, oneWebPageArr]);

    useEffect(() => {
      setTimeout(() => {
        const shadowDom = document.getElementById(SHADOW_SIDE_PANEL).shadowRoot;
        const div = shadowDom.querySelector(".knowledge-base-info");
        if (!div) return;
        const scrollableDistanceX = div.scrollWidth - div.clientWidth;
        if (scrollableDistanceX > 0) {
          // 是否有滚动条
          if (div.scrollLeft > 0) {
            // 距离左侧是否有距离
            setIsRightShow(true);
            setIsLeftShow(true);
          } else {
            setIsRightShow(true);
            setIsLeftShow(false);
          }
        } else {
          setIsRightShow(false);
          setIsLeftShow(false);
        }
      }, 300);
    }, [localFile, ocrCon, selectKnowledgeArr, localWebPageFile]);
    // 点击左右箭头
    const translateX = (type: number) => {
      const shadowDom = document.getElementById(SHADOW_SIDE_PANEL).shadowRoot;
      const div = shadowDom.querySelector(".knowledge-base-info");
      const scrollableDistanceX = div.scrollWidth - div.clientWidth;
      if (type == 1) {
        if (div.scrollLeft + 114 > scrollableDistanceX) {
          if (scrollableDistanceX > 0) {
            setIsRightShow(false);
            setIsLeftShow(true);
          }
          div.scrollTo({
            left: scrollableDistanceX,
            behavior: "smooth", // 如果想要平滑滚动，可以加上此选项
          });
        } else {
          if (scrollableDistanceX > 0) {
            setIsRightShow(true);
            setIsLeftShow(true);
          }
          div.scrollTo({
            left: div.scrollLeft + 114,
            behavior: "smooth", // 如果想要平滑滚动，可以加上此选项
          });
        }
      } else {
        if (div.scrollLeft - 114 < 0) {
          if (scrollableDistanceX > 0) {
            setIsLeftShow(false);
            setIsRightShow(true);
          }
          div.scrollTo({
            left: 0,
            behavior: "smooth", // 如果想要平滑滚动，可以加上此选项
          });
        } else {
          if (scrollableDistanceX > 0) {
            setIsRightShow(true);
            setIsLeftShow(true);
          }
          div.scrollTo({
            left: div.scrollLeft - 114,
            behavior: "smooth", // 如果想要平滑滚动，可以加上此选项
          });
        }
      }
    };
    // 删除截图信息
    const quoteClose = () => {
      setCurrentData({ image: "", texts: [] });
      setImageList([]);
      setOcrCon("");
    };
    // 换行
    const handleNewline = () => {
      setQuery((prevValue) => prevValue + "\n"); // 在当前文本后添加换行符
    };
    // 开启截图
    const screenshotScreen = async () => {
      browser.runtime.sendMessage({ type: "screenshot" });
    };
    /** 处理提示词搜索 */
    const handlePromptSearch = (queryText: string, currentAi: string | number, aiList: any) => {
      fetchRequest({
        api: "getAcctIns",
        params: {
          queryContent: "",
          insShowType: "2",
        },
        callback(res) {
          if (res.code === 200) {
            let arrShow = [];
            res.data.forEach((item) => {
              if (item.accountShowFlag !== 0) {
                arrShow.push(item);
              }
            });
            setPromptList(
              arrShow.map((item) => ({
                key: item.id,
                label: (
                  <Flex align="center" justify="space-between">
                    <span>{item.name}</span>
                    {/* 指令请求一次消耗积分显示 */}
                    {!!item.pointsCost && item.pointsCost > 0 && (
                      <Flex align="center">
                        <span>{item.pointsCost}&nbsp;</span>
                        <IconFont type="PointsCost" className="icon" />
                      </Flex>
                    )}
                  </Flex>
                ),
              })),
            );
            setPromptData(arrShow);
            setDropdownVisible(true);
            setLoading(false);
          } else {
            message.open({
              type: "error",
              content: res.msg,
            });
          }
        },
      });
    };
    /** 防抖搜索提示词 */
    const debouncePromptSearch = useCallback(debounce(handlePromptSearch, 500), []);
    // 输入框
    const content = (
      <Flex>
        <Input
          placeholder="输入内容后按回车"
          addonAfter={<EnterOutlined style={{ color: "#1890ff" }} />}
          onPressEnter={(e) => {
            console.log("回车键被按下", e.target.value);
            // 这里添加你的回车处理逻辑
          }}
        />
      </Flex>
    );
    useEffect(() => {
      onQueryChange?.(query); // 通知父组件
    }, [query]);

    // 将这个方法吐出，
    useImperativeHandle(ref, () => ({
      getMentionsData: () => {
        return {
          ocrCon,
          localWebPageFile,
          selectKnowledgeArr,
          oneWebPageArr,
          imageList,
          localFile,
          query: getQuery(),
        };
      },
      setMentionsData: (data: any) => {
        if (data.ocrCon !== undefined) setOcrCon(data.ocrCon);
        if (data.localFile !== undefined) setLocalFile(data.localFile);
        if (data.localWebPageFile !== undefined) setLocalWebPageFile(data.localWebPageFile);
        if (data.selectKnowledgeArr !== undefined) setSelectKnowledgeArr(data.selectKnowledgeArr);
        if (data.oneWebPageArr !== undefined) setOneWebPageArr(data.oneWebPageArr);
        if (data.imageList !== undefined) setImageList(data.imageList);
        if (data.query !== undefined) setQuery(data.query);
      },
    }));
    return (
      <Flex className="mentions-components">
        <Flex className="text-input" vertical>
          <Flex className="chat-textarea" vertical>
            <Mentions
              prefix="/"
              autoFocus={true}
              className="text-input-mentions"
              placeholder="输入要撰写的主题、创作背景、要点"
              rows={4}
              value={query}
              maxLength={2000}
              options={[]}
              onInput={(e) => {
                let value = (e.target as HTMLInputElement).value;
                // 检查内容是否只包含空格或回车符
                if (/^[\s]*$/.test(value)) {
                  setQuery(""); // 如果只包含空格或回车符，清空输入框
                } else {
                  setQuery(value); // 否则更新输入内容
                }
              }}
              onSearch={(text) => {
                if (dropdownVisible) {
                  setDropdownVisible(false);
                } else {
                  setLoading(true);
                  debouncePromptSearch(text, currentAi, aiList);
                }
              }}
            />
            <Flex vertical style={{ padding: "0px 12px 12px" }}>
              <Divider
                orientation="left"
                textPaddingInline={csstoken.paddingXXS}
                style={{ color: csstoken.colorTextTertiary, fontSize: "10px", margin: "0px", fontWeight: "normal" }}
              >
                素材参考
              </Divider>
              <Flex className="top-toolbar" justify="space-between" align="center">
                <Flex className="top-toolbar-left" align="center">
                  <Flex align="center" gap={csstoken.marginXXS}>
                    {permissions.includes("ai:assistant:chat:upload") && (
                      <Flex className="upload-file">
                        <Dropdown
                          placement="top"
                          trigger={["click"]}
                          autoAdjustOverflow={true}
                          getPopupContainer={() => {
                            const shadowDom = document.getElementById("shadow-side-panel").shadowRoot;
                            return shadowDom.getElementById("sinoFolderAddOutlined");
                          }}
                          open={dropdownOpen}
                          onOpenChange={(open) => {
                            setDropdownOpen(open);
                            if (open) {
                              setKonwTooltipOpen(false);
                            }
                          }}
                          menu={{
                            items,
                            onClick: (key) => fileHandler(key),
                            selectable: true,
                          }}
                          arrow={false}
                        >
                          <Tooltip
                            placement="top"
                            open={konwTooltipOpen}
                            onOpenChange={setKonwTooltipOpen}
                            title={
                              <Flex>
                                <ul style={{ margin: 0, padding: 0, width: "100%" }}>
                                  <li>支持：docx,pptx,xls,xlsx,csv,txt,pdf</li>
                                  <li>限制：单个文件15MB;最多5个附件</li>
                                </ul>
                              </Flex>
                            }
                            getPopupContainer={() => {
                              const shadowDom = document.getElementById("shadow-side-panel").shadowRoot;
                              return shadowDom.getElementById("sinoFolderAddOutlined");
                            }}
                          >
                            <Space style={{ cursor: "pointer" }}>
                              <Button
                                type="text"
                                id="sinoFolderAddOutlined"
                                icon={<FolderAddOutlined className="icon" />}
                                className="btn-icon"
                              />
                            </Space>
                          </Tooltip>
                        </Dropdown>
                      </Flex>
                    )}
                    {/* <Tooltip
                    placement="top"
                    title="网址"
                    getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                  >
                    <Space style={{ cursor: "pointer" }}>
                      <Popover
                        content={content}
                        title="Title"
                        trigger="click"
                        getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                      >
                        <Button icon={<PaperClipOutlined className="icon" />} className="btn-icon" type="text" />
                      </Popover>
                    </Space>
                  </Tooltip> */}
                    <Tooltip
                      placement="top"
                      title="截屏"
                      getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
                    >
                      <Space style={{ cursor: "pointer" }}>
                        <Button
                          type="text"
                          icon={<ScissorOutlined className="icon" />}
                          className="btn-icon"
                          onClick={screenshotScreen}
                        />
                      </Space>
                    </Tooltip>
                  </Flex>
                </Flex>
              </Flex>
              <Flex className={classNames({ "knowledge-base": allFile.length > 0 || ocrCon })}>
                {allFile && (isRightShow || isLeftShow) && (
                  <Flex>
                    {isRightShow && (
                      <Flex className="right-icon">
                        <Button
                          shape="circle"
                          icon={<RightOutlined />}
                          className="right"
                          onClick={() => translateX(1)}
                        />
                      </Flex>
                    )}
                    {isLeftShow && (
                      <Flex className="left-icon">
                        <Button shape="circle" icon={<LeftOutlined />} className="left" onClick={() => translateX(2)} />
                      </Flex>
                    )}
                  </Flex>
                )}
                <Flex className="knowledge-base-info">
                  {ocrCon && (
                    <Flex className="text-input-content" vertical>
                      <Flex className="quote-text">{ocrCon}</Flex>
                      <CloseCircleFilled className="quote-close" onClick={quoteClose} />
                    </Flex>
                  )}
                  {allFile &&
                    allFile.map((item, index) => {
                      return (
                        <Flex
                          key={index}
                          className={classNames("knowledge-content-base", {
                            "knowledge-content-base-width": allFile.length > 1 && !item.loading,
                          })}
                        >
                          {item.loading && (
                            <Flex className="knowledge-load" align="center">
                              <Spin spinning={item.loading}></Spin>
                            </Flex>
                          )}
                          <Flex
                            className="knowledge-content-base-flex"
                            align="center"
                            style={{ width: !item.loading ? "100%" : "calc(100% - 28px)" }}
                          >
                            <Flex className="knowledge-content-base-item" align="center">
                              {item.flag === "knowledge" && (
                                <Flex className="sino-relation-icon">{knowdgeSVGIcon.mainImage1}</Flex>
                              )}
                              {item.flag === "file" && fileExtensionHandler(item)}
                              {(item.flag === "webPageFile" || item.flag === "oneWebPageFile") && (
                                <img width="24" height="24" src={item.faviocn} alt="" />
                              )}
                              <Flex className="knowledge-base-item first-title" vertical justify="center">
                                <div className="knowledge-base-title">{item.libName}</div>
                                <div className="two-title">{item.libDesc}</div>
                              </Flex>
                            </Flex>
                            {!item.loading && (
                              <CloseCircleFilled className="close" onClick={() => handleDeleteKnowledge(item, index)} />
                            )}
                          </Flex>
                        </Flex>
                      );
                    })}
                </Flex>
              </Flex>
            </Flex>
          </Flex>
        </Flex>
        {knowledModel && (
          <Knowledge
            cardData={cardData}
            knowledModel={knowledModel}
            knowledLoading={knowledLoading}
            keywordSearch={keywordSearch}
            keywordValue={searchParams?.entity?.libName}
            closeKnowledModel={closeKnowledModel}
            closeKnowledModelFalg={closeKnowledModelFalg}
            onCheckboxChange={handleCheckboxChange}
          ></Knowledge>
        )}
        {webPageFileModal && (
          <WebPageFilechild
            webCardData={webPageFile}
            webPageFileModal={webPageFileModal}
            closeWebPageModel={closeWebPageModel}
            closeWebPageFalg={closeWebPageFalg}
            onCheckboxWebPageChange={handleCheckboxWebPageChange}
          ></WebPageFilechild>
        )}
      </Flex>
    );
  },
);
// 添加 displayName
MentionsComponent.displayName = "MentionsComponent";
export default MentionsComponent;
