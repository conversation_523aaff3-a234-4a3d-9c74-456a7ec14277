/** markdown渲染组件 */
import React, { useState, ReactNode } from "react";
import ReactMarkdown from "react-markdown";
import RemarkMath from "remark-math";
import RemarkGfm from "remark-gfm";
import RemarkBreaks from "remark-breaks";
import rehypeRaw from "rehype-raw";
import ThinkBlock from "./think";
import Round from "./round";
// import RemarkMermaid from "remark-mermaid"; // 引入remark-mermaid插件
import SyntaxHighLighter from "react-syntax-highlighter";
import { solarizedLight } from "react-syntax-highlighter/dist/esm/styles/hljs";
import {
  ArrowRightOutlined,
  BulbOutlined,
  CodeSandboxOutlined,
  CopyOutlined,
  DownloadOutlined,
  FilePdfOutlined,
} from "@ant-design/icons";
import { Button, Collapse, Flex, Popover, Progress, Space, Spin, theme, Tooltip } from "antd";
import useClipboard from "@/hooks/useClipboard.ts";
import { copyText } from "@/utils/clipboard.ts";
import IconFont from "@/components/IconFont";
import "katex/dist/katex.min.css";
import "./index.less";

const { useToken } = theme;

interface MarkdownProps {
  content: string;
  finished: boolean;
}

interface ImageWithOverlayProps {
  src: string;
  alt: string;
}

interface UrlComProps {
  href: string;
  children: string;
}

const Markdown: React.FC<MarkdownProps> = ({ content, finished = true }) => {
  const fetchRequest = useFetchRequest();
  const clipboard = useClipboard();
  const { token } = useToken();

  // Process think tags and extract their content
  const { processedContent, thinkBlocks } = React.useMemo(() => {
    const thinkBlocks: { [key: string]: string } = {};

    // Ensure content is a string to prevent "Cannot read properties of undefined" errors
    const safeContent = content || "";

    // 检查是否有未闭合的 think 标签
    let hasUnclosedThink = safeContent.includes("<think>") && !safeContent.includes("</think>");

    // 如果有未闭合的 think 标签，需要特殊处理
    if (hasUnclosedThink && !finished) {
      // 找到最后一个 <think> 标签的位置
      const lastThinkIndex = safeContent.lastIndexOf("<think>");
      // 提取 <think> 之前的内容和 <think> 之后的内容
      const beforeThink = safeContent.substring(0, lastThinkIndex);
      const afterThink = safeContent.substring(lastThinkIndex + 7); // 7 是 <think> 的长度

      // 处理 <think> 之前的内容中的完整 think 标签
      const processedBefore = beforeThink.replace(/<think>([\s\S]*?)<\/think>/g, (_, thinkContent) => {
        const thinkId = `think-${Math.random().toString(36).substring(2, 10)}`;
        thinkBlocks[thinkId] = thinkContent.trim();
        return `<think data-id="${thinkId}"></think>`;
      });

      // 为未闭合的 think 标签创建一个 ID
      const unclosedThinkId = `think-${Math.random().toString(36).substring(2, 10)}`;
      // 存储未闭合 think 标签的内容
      thinkBlocks[unclosedThinkId] = afterThink.trim();

      // 组合处理后的内容
      return {
        processedContent: `${processedBefore}<think data-id="${unclosedThinkId}"></think>`,
        thinkBlocks,
      };
    } else {
      // 正常处理完整的 think 标签
      const processed = safeContent.replace(/<think>([\s\S]*?)<\/think>/g, (_, thinkContent) => {
        // Create a unique ID for each think block
        const thinkId = `think-${Math.random().toString(36).substring(2, 10)}`;
        // Store the think content with its ID
        thinkBlocks[thinkId] = thinkContent.trim();
        // Replace with a placeholder that won't interfere with other markdown
        return `<think data-id="${thinkId}"></think>`;
      });

      return { processedContent: processed, thinkBlocks };
    }
  }, [content, finished]);

  // const markTag = (children: ReactNode, isLi: boolean = true): ReactNode => {
  //   return isLi ? (
  //     <li>{renderChildren(children)}</li>
  //   ) : (
  //     <span>
  //       {renderChildren(children)}
  //       <br />
  //     </span>
  //   );
  // };

  // const renderChildren = (children: ReactNode): ReactNode => {
  //   return React.Children.map(children, (child) => {
  //     if (React.isValidElement(child)) {
  //       // 处理 <p> 标签
  //       if (child.type === "p") {
  //         const content = Array.isArray(child.props.children) ? (
  //           child.props.children.map((text: React.ReactNode, index: number) => (
  //             <React.Fragment key={index}>{text}</React.Fragment>
  //           ))
  //         ) : (
  //           <React.Fragment>{child.props.children}</React.Fragment>
  //         );
  //         return (
  //           <React.Fragment key={child.key || Math.random()}>
  //             {content}
  //             {/*<br />*/}
  //           </React.Fragment>
  //         );
  //       }

  //       // 处理 <ul> 标签
  //       if (child.type === "ul" || child.type === "ol") {
  //         const nestedContent = React.Children.map(child.props.children, (nestedChild: any) => {
  //           if (React.isValidElement(nestedChild) && (nestedChild.props as any).node.tagName === "li") {
  //             return markTag((nestedChild.props as any).children, false);
  //           }
  //           return nestedChild; // Return other children unmodified
  //         });
  //         return (
  //           <React.Fragment key={child.key || Math.random()}>
  //             <br />
  //             <span className="list-item">{nestedContent}</span>
  //           </React.Fragment>
  //         );
  //       }

  //       // 其他元素直接返回
  //       return child;
  //     }
  //     return child; // 如果不是有效的元素，直接返回
  //   });
  // };

  // 图片鼠标移入处理
  const ImageWithOverlay: React.FC<ImageWithOverlayProps> = ({ src, alt }) => {
    let imgUrl = `${import.meta.env["VITE_FILE_PREFIX"]}${src}`;
    const [isHovered, setIsHovered] = useState(false);
    return (
      <div
        style={{ position: "relative", display: "inline-block" }}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <img
          src={imgUrl}
          alt={alt}
          style={{ maxWidth: "100%", height: "auto", display: "block", borderRadius: token.borderRadiusXS }}
        />
        {isHovered && (
          <div
            style={{
              width: "24px",
              height: "24px",
              position: "absolute",
              top: "9px",
              right: "12px",
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              display: "flex",
              borderRadius: "4px",
              cursor: "pointer",
              justifyContent: "center",
              alignContent: "center",
            }}
          >
            <DownloadOutlined style={{ fontSize: "14px", color: "#fff" }} />
          </div>
        )}
      </div>
    );
  };

  // 鼠标移入 链接处理
  const UrlCom: React.FC<UrlComProps> = ({ href, children }) => {
    let fileFormat = ["docx", "pptx", "xls", "xlsx", "csv", "txt", "pdf", "bin"]; // 文件格式
    const suffix = children?.split(".")[children?.split(".").length - 1]; // 获取后缀
    const isFileUrl = href.includes("/file-preview") || (href.includes("/files/tools") && fileFormat.includes(suffix)); // 是否是文件链接
    let linkUrl = "";
    if (isFileUrl) {
      linkUrl = `${import.meta.env["VITE_FILE_PREFIX"]}${href}`;
    } else {
      linkUrl = href;
    }
    return (
      <>
        {isFileUrl ? (
          <Flex style={{ position: "relative", display: "inline-block", width: "100%" }}>
            <Flex
              align="center"
              justify="space-between"
              style={{
                width: "100%",
              }}
            >
              <a
                href={linkUrl}
                download={children}
                target="_blank"
                rel="noopener noreferrer"
                style={{
                  maxWidth: "90%",
                  minWidth: "240px",
                  display: "inline-block",
                  overflow: "hidden",
                  color: token.colorLink,
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                }}
              >
                {children}
              </a>
            </Flex>
          </Flex>
        ) : (
          <a
            href={href}
            target="_blank"
            style={{
              maxWidth: "90%",
              minWidth: "240px",
              overflow: "hidden",
              textOverflow: "ellipsis",
              whiteSpace: "nowrap",
              color: token.colorLink,
            }}
            rel="noreferrer"
          >
            {children}
          </a>
        )}
      </>
    );
  };

  // 引用文本
  const quteContent = (section: any) => (
    <Flex
      gap={token.marginXS}
      style={{
        maxWidth: 300,
        maxHeight: 450,
        overflowY: "auto",
        overflowX: "hidden",
      }}
      vertical
    >
      {/* 文档标题区域 */}
      {/* <Flex
        style={{
          background: token.colorFillTertiary,
          padding: token.paddingXS,
          fontSize: token.fontSize,
          color: "#121212",
          lineHeight: "22px",
        }}
      >
        {section.title}
      </Flex> */}
      {section.contentList && section.contentList.length > 0 && (
        <>
          {section.contentList.map((item: any) => (
            <>
              {/* 文档内容区域 */}
              <Flex
                style={{
                  fontSize: token.fontSizeSM,
                  lineHeight: "20px",
                  color: token.colorTextSecondary,
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  display: "-webkit-box",
                  WebkitLineClamp: 5,
                  WebkitBoxOrient: "vertical",
                  wordBreak: "break-all",
                }}
              >
                {item.description}
              </Flex>
              <Flex gap={18}>
                <Progress
                  percent={item.score * 100}
                  showInfo={false}
                  strokeColor="#1890ff"
                  strokeWidth={6}
                  style={{ width: 130 }}
                />
                <span>{item.score}</span>
              </Flex>
            </>
          ))}
        </>
      )}
    </Flex>
  );
  // 打开代码编辑器页面
  const openBuildingUrl = (codeText: string, type: string) => {
    fetchRequest({
      api: "createShortUrl",
      params: {
        codeText,
      },
      callback: (res) => {
        if (res.code === 200) {
          // 找到最后一个斜杠的位置
          const lastSlashIndex = res.data.lastIndexOf("/");
          // 从最后一个斜杠后提取字符串
          const shortUrl = res.data.substring(lastSlashIndex + 1);
          window.open(
            `${import.meta.env["VITE_TOOLBOX_URL"]}/#/${type == "mermaid" ? "flowchart" : "interpreter"}?shortUrl=${shortUrl}&type=${type}`,
            "_blank",
          );
        }
      },
    });
  };

  const markdownComponents = {
    // round: (props: any) => {
    //   return <Round finished={finished}>{props.children}</Round>;
    // },
    code({ node, inline, className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || "");
      let title = [];
      if (!inline && match && ["html", "jsx", "tsx", "vue", "mermaid"].includes(match[1])) {
        title = /<title>(.*?)<\/title>/.exec(children);
      }
      // 这个地方强行加了个引用
      const raw = String(children).trim();
      if (match?.[1] === "js" && raw.startsWith("**引用**")) {
        let jsonStr = raw.replace(/^\*\*引用\*\*/, "").trim();
        const fenceIndex = jsonStr.indexOf("```");
        if (fenceIndex !== -1) {
          jsonStr = jsonStr.slice(0, fenceIndex).trim();
        }

        try {
          const refs = JSON.parse(jsonStr);
          // ✅ 这里就是你要的引用数组
          return (
            <div className="reference-box">
              <span className="reference-box-tit">引用</span>
              {refs.map((item: any, idx: number) => (
                <Popover
                  key={idx}
                  placement="right"
                  getPopupContainer={() => {
                    const shadowPanel = document.getElementById("shadow-side-panel");
                    return shadowPanel?.shadowRoot?.querySelector(".side-panel-content") || document.body;
                  }}
                  title={item.title}
                  content={quteContent(item)}
                >
                  <div className="reference-item">
                    <a href={item.path} target="_blank" rel="noopener noreferrer">
                      {item.title}
                    </a>
                  </div>
                </Popover>
              ))}
            </div>
          );
        } catch (e) {
          return (
            <div style={{ padding: 8, display: "flex", alignItems: "center", gap: 8 }}>
              <Spin size="small" />
              <span>正在加载引用文件...</span>
            </div>
          );
        }
      }
      const isHtmlPage =
        typeof children === "string" && (children.includes("</html>") || children.includes("export default"));
      return !inline && match ? (
        <>
          {["html", "jsx", "tsx", "vue", "mermaid"].includes(match[1]) ? (
            <Flex
              className="code-card"
              onClick={() => {
                if (finished || isHtmlPage) {
                  openBuildingUrl(children, match[1]);
                }
              }}
            >
              {/* <Flex className="code-card-icon">
                <IconFont type="Ts" className="icon" />
              </Flex> */}
              <Flex vertical>
                {["html"].includes(match[1]) && (
                  <Flex className="code-title">{title?.length > 0 ? title[1] : "Html"}</Flex>
                )}
                {["vue"].includes(match[1]) && <Flex className="code-title">Vue</Flex>}
                {["tsx", "jsx", "react"].includes(match[1]) && <Flex className="code-title">React</Flex>}
                {["mermaid"].includes(match[1]) && <Flex className="code-title">关系图</Flex>}
                {isHtmlPage || finished ? (
                  <Flex className="code-progress">已生成</Flex>
                ) : (
                  <Flex className="code-progress">生成中...</Flex>
                )}
              </Flex>
            </Flex>
          ) : (
            <SyntaxHighLighter
              {...props}
              children={String(children).replace(/\n$/, "")}
              style={solarizedLight}
              language={match[1]}
              showLineNumbers
            />
          )}
          {/* <Flex className="code-block-toolbar" justify="flex-end">
            <Space size={token.paddingXXS}>
              <Tooltip
                placement="top"
                title={clipboard.copied === children ? "已复制" : "复制"}
                getPopupContainer={(triggerNode) => triggerNode.parentNode as any}
              >
                <Button
                  style={{
                    padding: token.paddingXXS,
                    height: "24px",
                    width: "24px",
                    border: "none",
                    backgroundColor: token.colorBgMask,
                  }}
                  icon={<CopyOutlined style={{ color: token.colorTextLightSolid, width: "12px" }} />}
                  onClick={() => {
                    copyText(children).then(() => {
                      clipboard.setCopied(children);
                      setTimeout(() => {
                        clipboard.setCopied(null);
                      }, 2000);
                    });
                  }}
                />
              </Tooltip>

              {["html", "jsx", "tsx", "vue"].includes(match[1]) && (
                <Button
                  style={{
                    padding: token.paddingXXS,
                    height: "24px",
                    width: "24px",
                    border: "none",
                    backgroundColor: token.colorBgMask,
                  }}
                  icon={<CodeSandboxOutlined style={{ color: token.colorTextLightSolid, width: "12px" }} />}
                  onClick={() => {
                    openBuildingUrl(children, match[1]);
                  }}
                />
              )}
            </Space>
          </Flex> */}
          {/* 非解释执行状态，展示代码块源数据 */}
        </>
      ) : (
        <code {...props} className={className}>
          {children}
        </code>
      );
    },
    // li({ children }: any) {
    //   return markTag(children);
    // },
    a: ({ node, ...props }: any) => <UrlCom {...props} />,
    img: ({ node, ...props }: any) => <ImageWithOverlay {...props} />,
    think: (props: any) => {
      // Get the think ID from the data attribute
      const thinkId = props["data-id"];
      if (!thinkId || !thinkBlocks[thinkId]) {
        return null;
      }
      // Render the think content that was extracted during preprocessing
      // Pass the raw content string to ThinkBlock which will handle markdown rendering internally
      return <ThinkBlock finished={finished}>{thinkBlocks[thinkId]}</ThinkBlock>;
    },
  };

  const contentBlocks = React.useMemo(() => {
    const regex = /<round[^>]*>([\s\S]*?)<\/round>/gi;
    const blocks: { type: "message" | "round"; content: string }[] = [];
    let lastIndex = 0;
    let safeContent = content || "";
    let match;

    while ((match = regex.exec(safeContent)) !== null) {
      // round标签前的普通内容
      if (match.index > lastIndex) {
        const msg = safeContent.slice(lastIndex, match.index).trim();
        if (msg) blocks.push({ type: "message", content: msg });
      }
      // round标签内容
      blocks.push({ type: "round", content: match[1].trim() });
      lastIndex = regex.lastIndex;
    }

    // 检查是否有未闭合的 <round>
    const openRoundIdx = safeContent.lastIndexOf("<round");
    const closeRoundIdx = safeContent.lastIndexOf("</round>");
    if (openRoundIdx > closeRoundIdx && openRoundIdx !== -1) {
      const start = safeContent.indexOf(">", openRoundIdx);
      if (start !== -1) {
        const unclosedContent = safeContent.slice(start + 1).trim();
        if (unclosedContent) {
          blocks.push({ type: "round", content: unclosedContent });
        }
      }
    }

    // 最后一个普通内容
    if (lastIndex < safeContent.length && (openRoundIdx === -1 || lastIndex > openRoundIdx)) {
      const msg = safeContent.slice(lastIndex).trim();
      if (msg) blocks.push({ type: "message", content: msg });
    }
    return blocks;
  }, [content]);

  // 1. 提取 round 块
  const roundRegex = /<round[^>]*>([\s\S]*?)<\/round>/gi;
  const roundBlocks: string[] = [];
  let match: RegExpExecArray | null;
  while ((match = roundRegex.exec(content)) !== null) {
    roundBlocks.push(match[1].trim());
  }
  // 2. 提取 round 外的内容
  const processedContentWithoutRound = processedContent.replace(/<round[^>]*>[\s\S]*?<\/round>/gi, "").trim();

  // 判断所有 round 是否闭合
  const isAllRoundClosed = React.useMemo(() => {
    const openCount = (processedContent.match(/<round[^>]*>/g) || []).length;
    const closeCount = (processedContent.match(/<\/round>/g) || []).length;
    return openCount === closeCount;
  }, [content]);
  return (
    <div className={`sino-markdown-body ${finished ? "" : "markdown-blink"}`}>
      {contentBlocks.every((block) => block.type === "message") ? (
        // 没有 round，整体渲染
        <ReactMarkdown
          remarkPlugins={[[RemarkMath], RemarkGfm, RemarkBreaks]}
          rehypePlugins={[rehypeRaw]}
          components={markdownComponents as any}
        >
          {processedContent}
        </ReactMarkdown>
      ) : (
        <>
          {/* round标签内内容分别渲染，支持未闭合 */}
          {contentBlocks.map(
            (block, idx) =>
              block.type === "round" && (
                <Round key={`round-${idx}`} finished={finished}>
                  {block.content}
                </Round>
              ),
          )}
          {/* round标签外内容整体渲染，服用processedContent的所有处理 */}
          {isAllRoundClosed && processedContentWithoutRound && (
            <ReactMarkdown
              remarkPlugins={[[RemarkMath], RemarkGfm, RemarkBreaks]}
              rehypePlugins={[rehypeRaw]}
              components={markdownComponents as any}
            >
              {processedContentWithoutRound}
            </ReactMarkdown>
          )}
        </>
      )}
    </div>
  );
};

export default Markdown;
